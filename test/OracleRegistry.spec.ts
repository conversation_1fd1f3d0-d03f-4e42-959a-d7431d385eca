import { Wallet } from 'ethers'
import { ethers, waffle } from 'hardhat'
import { OracleRegistry } from '../typechain/OracleRegistry'
import { UniswapV3Factory } from '../typechain/UniswapV3Factory'
import { expect } from './shared/expect'

const TEST_ADDRESSES: [string, string, string] = [
  '******************************************',
  '******************************************',
  '******************************************'
]

const createFixtureLoader = waffle.createFixtureLoader

describe('OracleRegistry', () => {
  let wallet: Wallet, other: Wallet
  let registry: OracleRegistry
  let factory: UniswapV3Factory

  const fixture = async () => {
    const [wallet, other, ...rest] = await ethers.getSigners()
    
    const factoryFactory = await ethers.getContractFactory('UniswapV3Factory')
    factory = (await factoryFactory.deploy()) as UniswapV3Factory
    await factory.deployed()

    const poolLogicFactory = await ethers.getContractFactory('MockTimeUniswapV3Pool')
    const poolLogic = await poolLogicFactory.deploy()
    await poolLogic.deployed()

    const oracleRegistryFactory = await ethers.getContractFactory('OracleRegistry')
    registry = (await oracleRegistryFactory.deploy(factory.address)) as OracleRegistry
    await registry.deployed()

    await factory.initialize(wallet.address, registry.address, poolLogic.address)

    // Grant POOL_MANAGER_ROLE to all test signers
    const POOL_MANAGER_ROLE = await factory.POOL_MANAGER_ROLE()
    for (const signer of [wallet, other, ...rest]) {
      await factory.grantRole(POOL_MANAGER_ROLE, signer.address)
    }

    await factory.setOracleRegistry(registry.address)

    return registry
  }

  let loadFixture: ReturnType<typeof createFixtureLoader>
  before('create fixture loader', async () => {
    ;[wallet, other] = await (ethers as any).getSigners()
    loadFixture = createFixtureLoader([wallet, other])
  })

  beforeEach('deploy registry', async () => {
    registry = await loadFixture(fixture)
  })

  describe('constructor', () => {
    it('sets factory correctly', async () => {
      expect(await registry.uniswapV3Factory()).to.eq(factory.address)
    })
  })

  describe('#setOracle', () => {
    it('allows factory to set oracle', async () => {
      await expect(factory.setOracle(TEST_ADDRESSES[0], TEST_ADDRESSES[2]))
        .to.not.be.reverted

      expect(await registry.tokenOracles(TEST_ADDRESSES[0])).to.eq(TEST_ADDRESSES[2])
    })

    it('fails if caller is not factory', async () => {
      await expect(registry.connect(other).setOracle(TEST_ADDRESSES[0], TEST_ADDRESSES[2]))
        .to.be.revertedWith('Only Factory contract allowed')
    })

    it('allows updating existing oracle', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], TEST_ADDRESSES[2])
      await factory.setOracle(TEST_ADDRESSES[0], other.address)
      expect(await registry.tokenOracles(TEST_ADDRESSES[0])).to.eq(other.address)
    })

    it('allows setting oracle to zero address', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], TEST_ADDRESSES[2])
      await factory.setOracle(TEST_ADDRESSES[0], ethers.constants.AddressZero)
      expect(await registry.tokenOracles(TEST_ADDRESSES[0])).to.eq(ethers.constants.AddressZero)
    })
  })

  describe('#getOracle', () => {
    beforeEach('set up some oracles', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], TEST_ADDRESSES[2])
    })

    it('returns oracle if token has one', async () => {
      const result = await registry.getOracle(TEST_ADDRESSES[0])
      expect(result).to.eq(TEST_ADDRESSES[2])
    })

    it('returns zero address if token has no oracle', async () => {
      const result = await registry.getOracle(TEST_ADDRESSES[1])
      expect(result).to.eq(ethers.constants.AddressZero)
    })

    it('reverts with ZTA if token is zero address', async () => {
      await expect(registry.getOracle(ethers.constants.AddressZero)).to.be.revertedWith('ZTA')
    })

    it('returns zero address if oracle was set then removed', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], ethers.constants.AddressZero)
      const result = await registry.getOracle(TEST_ADDRESSES[0])
      expect(result).to.eq(ethers.constants.AddressZero)
    })
  })
}) 