import { ethers, waffle } from 'hardhat'
import { BigN<PERSON>ber, BigN<PERSON>berish, constants, Wallet, ContractFactory } from 'ethers'
import { TestERC20 } from '../typechain/TestERC20'
import { UniswapV3Factory } from '../typechain/UniswapV3Factory'
import { MockTimeUniswapV3Pool } from '../typechain/MockTimeUniswapV3Pool'
import { TestUniswapV3Callee } from '../typechain/TestUniswapV3Callee'
import { expect } from './shared/expect'
const { anyValue } = require('chai')

import { poolFixture } from './shared/fixtures'

import {
  FeeAmount,
  TICK_SPACINGS,
  createPoolFunctions,
  encodePriceSqrt,
  getMinTick,
  getMaxTick,
  expandTo18Decimals,
  SwapFunction,
  MintFunction,
  PoolFunctions
} from './shared/utilities'

const createFixtureLoader = waffle.createFixtureLoader

describe.skip('UniswapV3Pool ProjectManager function tests', () => {
  let wallet: Wallet, projectManager: Wallet, other: Wallet
  let token0: TestERC20
  let token1: TestERC20
  let factory: UniswapV3Factory
  let pool: MockTimeUniswapV3Pool
  let swapTarget: TestUniswapV3Callee
  let minTick: number
  let maxTick: number
  let poolFunctions: PoolFunctions
  let createPool: any

  let loadFixture: ReturnType<typeof createFixtureLoader>

  let lastTestTimestamp = 0;

  before('create fixture loader', async () => {
    ;[wallet, projectManager, other] = await (ethers as any).getSigners()
    loadFixture = createFixtureLoader([wallet, projectManager, other])
  })

  beforeEach('deploy fixture', async () => {
    const latestBlock = await ethers.provider.getBlock('latest');
    await ethers.provider.send('evm_setNextBlockTimestamp', [latestBlock.timestamp + 1]);
    await ethers.provider.send('evm_mine', []);
    const fixture = await loadFixture(poolFixture)
    token0 = fixture.token0
    token1 = fixture.token1
    factory = fixture.factory
    createPool = fixture.createPool
    swapTarget = fixture.swapTargetCallee

    // Grant DEFAULT_ADMIN_ROLE to wallet first
    const DEFAULT_ADMIN_ROLE = await factory.DEFAULT_ADMIN_ROLE()
    await factory.grantRole(DEFAULT_ADMIN_ROLE, wallet.address)

    // Grant OWNER_ROLE to wallet
    const OWNER_ROLE = await factory.OWNER_ROLE()
    await factory.grantRole(OWNER_ROLE, wallet.address)

    // Grant POOL_MANAGER_ROLE to projectManager and wallet before createPool
    const POOL_MANAGER_ROLE = await factory.POOL_MANAGER_ROLE()
    await factory.grantRole(POOL_MANAGER_ROLE, projectManager.address)
    await factory.grantRole(POOL_MANAGER_ROLE, wallet.address)

    // Set periphery to wallet address
    await factory.setPeriphery(wallet.address)

    pool = await createPool(FeeAmount.MEDIUM, TICK_SPACINGS[FeeAmount.MEDIUM], token0, token1, projectManager)
    minTick = getMinTick(TICK_SPACINGS[FeeAmount.MEDIUM])
    maxTick = getMaxTick(TICK_SPACINGS[FeeAmount.MEDIUM])
    poolFunctions = createPoolFunctions({
      swapTarget,
      token0,
      token1,
      pool,
    })

    // Give some tokens to project manager
    await token0.mint(projectManager.address, expandTo18Decimals(100))
    await token1.mint(projectManager.address, expandTo18Decimals(100))

    // Grant POOL_MANAGER_ROLE to all relevant signers
    for (const signer of [wallet, other, projectManager, swapTarget]) {
      await factory.grantRole(POOL_MANAGER_ROLE, signer.address)
    }

    await factory.setPeriphery(wallet.address)
  })

  it('initial setup is correct', async () => {
    expect(await pool.factory()).to.eq(factory.address)
    expect(await pool.token0()).to.eq(token0.address)
    expect(await pool.token1()).to.eq(token1.address)
  })

  describe('mintByProjectManager', () => {
    beforeEach('initialize the pool', async () => {
      const latestBlock = await ethers.provider.getBlock('latest');
      await ethers.provider.send('evm_setNextBlockTimestamp', [latestBlock.timestamp + 1]);
      await ethers.provider.send('evm_mine', []);
      await pool.initialize(encodePriceSqrt(1, 1))
    })

    describe('when called by project manager before ETP start', () => {
      beforeEach('set project manager', async () => {
        const latestBlock = await ethers.provider.getBlock('latest');
        // Use a unique fee to avoid PAE error
        const launchParams = {
          exclusiveTradingPeriodStart: latestBlock.timestamp + 1000,
          exclusiveTradingPeriodEnd: latestBlock.timestamp + 2000,
          projectManager: swapTarget.address
        };
        pool = await createPool(
          FeeAmount.HIGH, // Use HIGH to avoid duplicate pool
          TICK_SPACINGS[FeeAmount.HIGH],
          token0,
          token1,
          projectManager,
          launchParams
        );
        // Recalculate minTick and maxTick for FeeAmount.HIGH
        minTick = getMinTick(TICK_SPACINGS[FeeAmount.HIGH]);
        maxTick = getMaxTick(TICK_SPACINGS[FeeAmount.HIGH]);
        await factory.setProjectManager(pool.address, swapTarget.address);
        await pool.initialize(encodePriceSqrt(1, 1));
      })

      it('successfully mints a position', async () => {
        const liquidityAmount = expandTo18Decimals(1)
        await token0.connect(projectManager).approve(swapTarget.address, liquidityAmount)
        await token1.connect(projectManager).approve(swapTarget.address, liquidityAmount)

        await expect(swapTarget.connect(projectManager).mintByProjectManager(
          pool.address,
          projectManager.address,
          minTick + TICK_SPACINGS[FeeAmount.HIGH],
          maxTick - TICK_SPACINGS[FeeAmount.HIGH],
          liquidityAmount
        ))
          .to.emit(pool, 'MintByProjectManager')
          .withArgs(
            swapTarget.address,
            projectManager.address,
            minTick + TICK_SPACINGS[FeeAmount.HIGH],
            maxTick - TICK_SPACINGS[FeeAmount.HIGH],
            liquidityAmount,
            liquidityAmount,
            liquidityAmount
          )
      })

      it('transfers tokens to the pool', async () => {
        const liquidityAmount = expandTo18Decimals(1)
        await token0.connect(projectManager).approve(swapTarget.address, liquidityAmount)
        await token1.connect(projectManager).approve(swapTarget.address, liquidityAmount)

        await expect(swapTarget.connect(projectManager).mintByProjectManager(
          pool.address,
          projectManager.address,
          minTick + TICK_SPACINGS[FeeAmount.HIGH],
          maxTick - TICK_SPACINGS[FeeAmount.HIGH],
          liquidityAmount
        ))
          .to.emit(token0, 'Transfer')
          .withArgs(projectManager.address, pool.address, liquidityAmount)
          .to.emit(token1, 'Transfer')
          .withArgs(projectManager.address, pool.address, liquidityAmount)
      })

      it('updates the position', async () => {
        const liquidityAmount = expandTo18Decimals(1)
        await token0.connect(projectManager).approve(swapTarget.address, liquidityAmount)
        await token1.connect(projectManager).approve(swapTarget.address, liquidityAmount)

        await swapTarget.connect(projectManager).mintByProjectManager(
          pool.address,
          projectManager.address,
          minTick + TICK_SPACINGS[FeeAmount.HIGH],
          maxTick - TICK_SPACINGS[FeeAmount.HIGH],
          liquidityAmount
        )

        const { liquidity } = await pool.positions(
          ethers.utils.solidityKeccak256(
            ['address', 'int24', 'int24'],
            [projectManager.address, minTick + TICK_SPACINGS[FeeAmount.HIGH], maxTick - TICK_SPACINGS[FeeAmount.HIGH]]
          )
        )
        expect(liquidity).to.eq(liquidityAmount)
      })
    })

    describe('failure cases', () => {
      beforeEach('set project manager', async () => {
        const latestBlock = await ethers.provider.getBlock('latest');
        await ethers.provider.send('evm_setNextBlockTimestamp', [latestBlock.timestamp + 1]);
        await ethers.provider.send('evm_mine', []);
        await factory.setProjectManager(pool.address, swapTarget.address)
      })

      it('fails when called by non-project manager', async () => {
        const liquidityAmount = expandTo18Decimals(1)
        await token0.connect(other).approve(pool.address, liquidityAmount)
        await token1.connect(other).approve(pool.address, liquidityAmount)

        await expect(pool.connect(other).mintByProjectManager(
          other.address,
          minTick + TICK_SPACINGS[FeeAmount.MEDIUM],
          maxTick - TICK_SPACINGS[FeeAmount.MEDIUM],
          liquidityAmount,
          '0x'
        )).to.be.reverted
      })

      it('fails when called after ETP start', async () => {
        // Create a pool with ETP start in the past
        const { TEST_POOL_START_TIME } = require('./shared/fixtures')
        const pastETPStart = TEST_POOL_START_TIME - 1000
        const launchParams = {
          tokenLaunchType: 0, // FairLaunch
          exclusiveTradingPeriodStart: pastETPStart,
          exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 100000000000000,
          extendedLiquidityLockDuration: 0,
          projectManager: swapTarget.address
        }
        const newPool = await createPool(
          FeeAmount.LOW,
          TICK_SPACINGS[FeeAmount.LOW],
          token0,
          token1,
          projectManager,
          launchParams
        )
        await factory.setProjectManager(newPool.address, swapTarget.address)
        await newPool.initialize(encodePriceSqrt(1, 1))
        const liquidityAmount = expandTo18Decimals(1)
        await token0.connect(projectManager).approve(swapTarget.address, liquidityAmount)
        await token1.connect(projectManager).approve(swapTarget.address, liquidityAmount)
        await expect(swapTarget.connect(projectManager).mintByProjectManager(
          newPool.address,
          projectManager.address,
          minTick + TICK_SPACINGS[FeeAmount.LOW],
          maxTick - TICK_SPACINGS[FeeAmount.LOW],
          liquidityAmount
        )).to.be.reverted
      })

      it('fails with invalid tick range', async () => {
        const liquidityAmount = expandTo18Decimals(1)
        await token0.connect(projectManager).approve(swapTarget.address, liquidityAmount)
        await token1.connect(projectManager).approve(swapTarget.address, liquidityAmount)

        await expect(swapTarget.connect(projectManager).mintByProjectManager(
          pool.address,
          projectManager.address,
          maxTick,
          minTick,
          liquidityAmount
        )).to.be.reverted
      })

      it('fails with zero liquidity', async () => {
        await expect(swapTarget.connect(projectManager).mintByProjectManager(
          pool.address,
          projectManager.address,
          minTick + TICK_SPACINGS[FeeAmount.MEDIUM],
          maxTick - TICK_SPACINGS[FeeAmount.MEDIUM],
          0
        )).to.be.reverted
      })

      it('fails with insufficient token approval', async () => {
        const liquidityAmount = expandTo18Decimals(1)
        // Don't approve tokens

        await expect(swapTarget.connect(projectManager).mintByProjectManager(
          pool.address,
          projectManager.address,
          minTick + TICK_SPACINGS[FeeAmount.MEDIUM],
          maxTick - TICK_SPACINGS[FeeAmount.MEDIUM],
          liquidityAmount
        )).to.be.reverted
      })
    })
  })

  describe('burnByProjectManager', () => {
    beforeEach('initialize and mint position for project manager', async () => {
      const latestBlock = await ethers.provider.getBlock('latest');
      await ethers.provider.send('evm_setNextBlockTimestamp', [latestBlock.timestamp + 1]);
      await ethers.provider.send('evm_mine', []);
      await pool.initialize(encodePriceSqrt(1, 1))
      await factory.setProjectManager(pool.address, projectManager.address)
      // Give tokens and mint a position for project manager
      await token0.mint(projectManager.address, expandTo18Decimals(100))
      await token1.mint(projectManager.address, expandTo18Decimals(100))
      await token0.connect(projectManager).approve(swapTarget.address, expandTo18Decimals(10))
      await token1.connect(projectManager).approve(swapTarget.address, expandTo18Decimals(10))
      await swapTarget.connect(projectManager).mint(
        pool.address,
        projectManager.address,
        minTick + TICK_SPACINGS[FeeAmount.MEDIUM],
        maxTick - TICK_SPACINGS[FeeAmount.MEDIUM],
        expandTo18Decimals(1)
      )
      // Do not advance time here; each test will set time as needed
    })

    it('successfully burns liquidity by project manager after ETP+lock', async () => {
      // Fast forward time past ETP+lock
      const latestBlock = await ethers.provider.getBlock('latest');
      await ethers.provider.send('evm_setNextBlockTimestamp', [latestBlock.timestamp + 10]);
      await ethers.provider.send('evm_mine', []);
      const burnAmount = expandTo18Decimals(1)
      const tx = await pool.connect(projectManager).burnByProjectManager(
        minTick + TICK_SPACINGS[FeeAmount.MEDIUM],
        maxTick - TICK_SPACINGS[FeeAmount.MEDIUM],
        burnAmount
      )
      await expect(tx)
        .to.emit(pool, 'Burn')
    })

    it('fails when called by non-project manager', async () => {
      // Fast forward time past ETP+lock
      const latestBlock = await ethers.provider.getBlock('latest');
      await ethers.provider.send('evm_setNextBlockTimestamp', [latestBlock.timestamp + 10]);
      await ethers.provider.send('evm_mine', []);
      const burnAmount = expandTo18Decimals(1)
      await expect(
        pool.connect(other).burnByProjectManager(
          minTick + TICK_SPACINGS[FeeAmount.MEDIUM],
          maxTick - TICK_SPACINGS[FeeAmount.MEDIUM],
          burnAmount
        )
      ).to.be.reverted
    })

    it('fails when called before ETP+lock', async () => {
      // Set ETP+lock in the future
      const latestBlock = await ethers.provider.getBlock('latest');
      const launchParams = {
        exclusiveTradingPeriodStart: latestBlock.timestamp + 1000,
        exclusiveTradingPeriodEnd: latestBlock.timestamp + 2000,
        extendedLiquidityLockDuration: 1000,
        projectManager: projectManager.address
      };
      const newPool = await createPool(
        FeeAmount.LOW,
        TICK_SPACINGS[FeeAmount.LOW],
        token0,
        token1,
        projectManager,
        launchParams
      );
      await newPool.initialize(encodePriceSqrt(1, 1));
      await factory.setProjectManager(newPool.address, projectManager.address);
      const burnAmount = expandTo18Decimals(1);
      await expect(
        newPool.connect(projectManager).burnByProjectManager(
          minTick + TICK_SPACINGS[FeeAmount.LOW],
          maxTick - TICK_SPACINGS[FeeAmount.LOW],
          burnAmount
        )
      ).to.be.reverted
    })
  })

  describe.skip('ETP+liquidity burnByProjectManager checks for USDC, USDT, and WETH pools', () => {
    let wallet: Wallet, projectManager: Wallet
    let token1: TestERC20
    let pool: MockTimeUniswapV3Pool
    let createPool: any
    let factory: UniswapV3Factory
    let minTick: number
    let maxTick: number
    let loadFixture: ReturnType<typeof waffle.createFixtureLoader>
    let poolFixtureData: any
    const FeeAmount = 3000
    const TickSpacing = 60
    const expand = expandTo18Decimals

    before('create fixture loader', async () => {
      [wallet, projectManager] = await (ethers as any).getSigners()
      loadFixture = waffle.createFixtureLoader([wallet, projectManager])
    })

    beforeEach(async () => {
      // Always increment the timestamp for each test
      const latestBlock = await ethers.provider.getBlock('latest');
      lastTestTimestamp = Math.max(lastTestTimestamp + 10, latestBlock.timestamp + 10);
      await ethers.provider.send('evm_setNextBlockTimestamp', [lastTestTimestamp]);
      await ethers.provider.send('evm_mine', []);
      poolFixtureData = await loadFixture(poolFixture)
      // Deploy a new token1 for each test to ensure unique pool
      const ERC20Factory: ContractFactory = await ethers.getContractFactory('TestERC20')
      token1 = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), 18) as TestERC20
      await token1.deployed()
      createPool = poolFixtureData.createPool
      factory = poolFixtureData.factory
      minTick = getMinTick(TickSpacing)
      maxTick = getMaxTick(TickSpacing)
    })

    async function setupStablePool({
      stableDecimals,
      stableOraclePrice,
      stableCoinAmount,
      launchType = 0,
      etpStartOffset = 10,
      etpEndOffset = 1000,
      mintStable = false,
      token1Override = token1,
      stableOverride = token1,
    }: {
      stableDecimals: number,
      stableOraclePrice: number,
      stableCoinAmount: BigNumber,
      launchType?: number,
      etpStartOffset?: number,
      etpEndOffset?: number,
      mintStable?: boolean,
      token1Override?: TestERC20,
      stableOverride?: TestERC20
    }) {
      // Set oracle price
      const MockChainlinkOracleFactory = await ethers.getContractFactory('MockChainlinkOracle');
      const oracle = await MockChainlinkOracleFactory.deploy(ethers.utils.parseUnits(stableOraclePrice.toString(), 8));
      await oracle.deployed();
      await poolFixtureData.factory.setOracle(stableOverride.address, oracle.address);
      // Create pool
      const now = (await ethers.provider.getBlock('latest')).timestamp
      pool = await createPool(FeeAmount, TickSpacing, stableOverride, token1Override, projectManager, {
        tokenLaunchType: launchType,
        exclusiveTradingPeriodStart: now + etpStartOffset,
        exclusiveTradingPeriodEnd: now + etpEndOffset,
        extendedLiquidityLockDuration: 0,
        projectManager: projectManager.address,
      })
      await pool.initialize(encodePriceSqrt(1, 1))
      await stableOverride.mint(projectManager.address, expand(100))
      await token1Override.mint(projectManager.address, expand(100))
      await stableOverride.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
      await token1Override.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
      await pool.connect(projectManager).mintByProjectManager(
        projectManager.address,
        minTick,
        maxTick,
        expand(1),
        '0x'
      )
      if (launchType === 0) {
        await pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.BigNumber.from(200_000).mul(ethers.BigNumber.from(10).pow(18)))
      } else {
        await pool.updateRequiredStableCoinLiquidityCuratedLaunch(ethers.BigNumber.from(400_000).mul(ethers.BigNumber.from(10).pow(18)))
      }
      if (mintStable && ethers.BigNumber.from(stableCoinAmount).gt(0)) {
        await stableOverride.transfer(pool.address, stableCoinAmount)
      }
      return { pool, stable: stableOverride }
    }

    for (const {symbol, decimals, oraclePrice} of [
      {symbol: 'USDC', decimals: 6, oraclePrice: 1},
      {symbol: 'USDT', decimals: 6, oraclePrice: 1},
      {symbol: 'WETH', decimals: 18, oraclePrice: 2000},
    ]) {
      describe(`${symbol}/Token pool`, () => {
        beforeEach(async () => {
          lastTestTimestamp += 10;
          const latestBlock = await ethers.provider.getBlock('latest');
          lastTestTimestamp = Math.max(lastTestTimestamp, latestBlock.timestamp + 10);
          await ethers.provider.send('evm_setNextBlockTimestamp', [lastTestTimestamp]);
          await ethers.provider.send('evm_mine', []);
        });
        it(`fails: burnByProjectManager during ETP but not enough ${symbol} (Fair)`, async () => {
          const ERC20Factory: ContractFactory = await ethers.getContractFactory('TestERC20')
          // Deploy new tokens for each test
          const token1Local = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), 18) as TestERC20
          await token1Local.deployed()
          const stableLocal = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), decimals) as TestERC20
          await stableLocal.deployed()
          // Approve swapTarget for both tokens
          await stableLocal.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
          await token1Local.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
          const { pool } = await setupStablePool({
            stableDecimals: decimals,
            stableOraclePrice: oraclePrice,
            stableCoinAmount: ethers.BigNumber.from(1_000_000),
            launchType: 0,
            etpStartOffset: -10,
            etpEndOffset: 1000,
            mintStable: true,
            token1Override: token1Local,
            stableOverride: stableLocal,
          })
          poolFunctions = createPoolFunctions({
            pool,
            token0: stableLocal,
            token1: token1Local,
            swapTarget,
          })
          await expect(pool.connect(projectManager).burnByProjectManager(
            minTick,
            maxTick,
            expand(1)
          )).to.be.revertedWith('ETP')
        })
        it(`succeeds: burnByProjectManager during ETP with enough ${symbol} (Fair)`, async () => {
          const ERC20Factory: ContractFactory = await ethers.getContractFactory('TestERC20')
          // Deploy new tokens for each test
          const token1Local = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), 18) as TestERC20
          await token1Local.deployed()
          const stableLocal = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), decimals) as TestERC20
          await stableLocal.deployed()
          // Approve swapTarget for both tokens
          await stableLocal.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
          await token1Local.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
          const required = ethers.BigNumber.from(200_000).mul(ethers.BigNumber.from(10).pow(decimals));
          const { pool } = await setupStablePool({
            stableDecimals: decimals,
            stableOraclePrice: oraclePrice,
            stableCoinAmount: required,
            launchType: 0,
            etpStartOffset: -10,
            etpEndOffset: 1000,
            mintStable: true,
            token1Override: token1Local,
            stableOverride: stableLocal,
          })
          poolFunctions = createPoolFunctions({
            pool,
            token0: stableLocal,
            token1: token1Local,
            swapTarget,
          })
          await expect(pool.connect(projectManager).burnByProjectManager(
            minTick,
            maxTick,
            expand(1)
          )).to.not.be.reverted
        })
        it(`fails: burnByProjectManager during ETP but not enough ${symbol} (Curated)`, async () => {
          const ERC20Factory: ContractFactory = await ethers.getContractFactory('TestERC20')
          // Deploy new tokens for each test
          const token1Local = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), 18) as TestERC20
          await token1Local.deployed()
          const stableLocal = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), decimals) as TestERC20
          await stableLocal.deployed()
          // Approve swapTarget for both tokens
          await stableLocal.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
          await token1Local.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
          const { pool } = await setupStablePool({
            stableDecimals: decimals,
            stableOraclePrice: oraclePrice,
            stableCoinAmount: ethers.BigNumber.from(1_000_000),
            launchType: 1,
            etpStartOffset: -10,
            etpEndOffset: 1000,
            mintStable: true,
            token1Override: token1Local,
            stableOverride: stableLocal,
          })
          poolFunctions = createPoolFunctions({
            pool,
            token0: stableLocal,
            token1: token1Local,
            swapTarget,
          })
          await expect(pool.connect(projectManager).burnByProjectManager(
            minTick,
            maxTick,
            expand(1)
          )).to.be.revertedWith('ETP')
        })
        it(`succeeds: burnByProjectManager during ETP with enough ${symbol} (Curated)`, async () => {
          const ERC20Factory: ContractFactory = await ethers.getContractFactory('TestERC20')
          // Deploy new tokens for each test
          const token1Local = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), 18) as TestERC20
          await token1Local.deployed()
          const stableLocal = await ERC20Factory.deploy(ethers.BigNumber.from(2).pow(255), decimals) as TestERC20
          await stableLocal.deployed()
          // Approve swapTarget for both tokens
          await stableLocal.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
          await token1Local.connect(projectManager).approve(swapTarget.address, ethers.BigNumber.from(2).pow(255))
          const required = ethers.BigNumber.from(400_000).mul(ethers.BigNumber.from(10).pow(decimals));
          const { pool } = await setupStablePool({
            stableDecimals: decimals,
            stableOraclePrice: oraclePrice,
            stableCoinAmount: required,
            launchType: 1,
            etpStartOffset: -10,
            etpEndOffset: 1000,
            mintStable: true,
            token1Override: token1Local,
            stableOverride: stableLocal,
          })
          poolFunctions = createPoolFunctions({
            pool,
            token0: stableLocal,
            token1: token1Local,
            swapTarget,
          })
          await expect(pool.connect(projectManager).burnByProjectManager(
            minTick,
            maxTick,
            expand(1)
          )).to.not.be.reverted
        })
      })
    }
  })
}) 