import { Wallet } from 'ethers'
import { ethers, waffle } from 'hardhat'
import { UniswapV3Factory } from '../typechain/UniswapV3Factory'
import { OracleRegistry } from '../typechain/OracleRegistry'
import { UniswapV3Pool } from '../typechain/UniswapV3Pool'
import { TestUniswapV3PoolV2 } from '../typechain/TestUniswapV3PoolV2'
import { IUniswapV3Pool } from '../typechain/IUniswapV3Pool'
import { expect } from './shared/expect'
import snapshotGasCost from './shared/snapshotGasCost'
import { TEST_POOL_START_TIME } from './shared/fixtures'

import { FeeAmount, getCreate2Address, TICK_SPACINGS } from './shared/utilities'

// Import Hardhat Upgrades plugin
import { upgrades } from 'hardhat'
// Import the type for the new V2 Factory
import { TestUniswapV3FactoryV2 } from '../typechain/TestUniswapV3FactoryV2'
import { TestERC20 } from '../typechain/TestERC20'
import { MockVault } from '../typechain/MockVault'
import { encodePriceSqrt } from './shared/utilities'

const { constants } = ethers

const TEST_ADDRESSES: [string, string] = [
  '******************************************',
  '******************************************',
]

const TEST_PROJECT_MANAGER = '******************************************'

const createFixtureLoader = waffle.createFixtureLoader

describe('UniswapV3Factory', () => {
  let wallet: Wallet, other: Wallet
  let initialPoolLogic: UniswapV3Pool

  let factory: UniswapV3Factory
  let oracleRegistry: OracleRegistry
  let mockVault: MockVault
  let poolBytecode: string

  interface FactoryTestFixture {
    factory: UniswapV3Factory
    oracleRegistry: OracleRegistry
    poolLogic: UniswapV3Pool
    mockVault: MockVault
  }

  const fixture = async (): Promise<FactoryTestFixture> => {
    const [wallet, other, ...rest] = await ethers.getSigners()
    const PoolLogicFactory = await ethers.getContractFactory('UniswapV3Pool')
    const poolLogic = (await PoolLogicFactory.deploy()) as UniswapV3Pool
    await poolLogic.deployed()

    const MockVaultFactory = await ethers.getContractFactory('MockVault')
    const mockVault = (await MockVaultFactory.deploy()) as MockVault
    await mockVault.deployed()

    const FactoryFactory = await ethers.getContractFactory('UniswapV3Factory')
    const factory = (await FactoryFactory.deploy()) as UniswapV3Factory
    await factory.deployed()

    const OracleRegistryFactory = await ethers.getContractFactory('OracleRegistry')
    const oracleRegistry = (await OracleRegistryFactory.deploy(factory.address)) as OracleRegistry
    await oracleRegistry.deployed()

    await factory.initialize(wallet.address, oracleRegistry.address, poolLogic.address)
    await factory.setPeriphery(wallet.address)
    
     // Set the vault in the factory
    await factory.setVault(mockVault.address)

    // Grant POOL_MANAGER_ROLE to all test signers
    const POOL_MANAGER_ROLE = await factory.POOL_MANAGER_ROLE()
    for (const signer of [wallet, other, ...rest]) {
      await factory.grantRole(POOL_MANAGER_ROLE, signer.address)
    }

    return { factory, oracleRegistry, poolLogic, mockVault }
  }

  let loadFixture: ReturnType<typeof createFixtureLoader>
  before('create fixture loader', async () => {
    ;[wallet, other] = await (ethers as any).getSigners()
    loadFixture = createFixtureLoader([wallet, other])
  })

  before('load pool bytecode', async () => {
    poolBytecode = (await ethers.getContractFactory('UniswapV3Pool')).bytecode
  })

  beforeEach('deploy contracts', async () => {
    const {
      factory: deployedFactory,
      oracleRegistry: deployedOracleRegistry,
      poolLogic,
      mockVault: deployedMockVault,
    } = await loadFixture(fixture)
    factory = deployedFactory
    oracleRegistry = deployedOracleRegistry
    initialPoolLogic = poolLogic
    mockVault = deployedMockVault
  })

  it('owner is deployer', async () => {
    expect(await factory.hasRole(constants.HashZero, wallet.address)).to.be.true
  })

  it('factory bytecode size', async () => {
    expect(((await waffle.provider.getCode(factory.address)).length - 2) / 2).to.matchSnapshot()
  })

  it('pool bytecode size', async () => {
    await factory.setOracle(TEST_ADDRESSES[0], wallet.address)
    const launchParams = {
      tokenLaunchType: 0, // Assuming FairLaunch is 0, adjust if necessary
      exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
      exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
      extendedLiquidityLockDuration: 0,
      projectManager: wallet.address,
      whitelistedAddressForSwap: wallet.address,
    }
    const tx = await factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.MEDIUM, launchParams)
    const receipt = await tx.wait()
    const poolCreatedEvent = receipt.events?.find((e) => e.event === 'PoolCreated')
    const poolAddress = poolCreatedEvent?.args?.pool
    expect(poolAddress).to.be.properAddress
    // The bytecode of the BeaconProxy itself, not the logic contract or old CREATE2 pool.
    // This might still be a meaningful snapshot if BeaconProxy bytecode is consistent.
    expect(((await waffle.provider.getCode(poolAddress)).length - 2) / 2).to.matchSnapshot()
  })

  it('initial enabled fee amounts', async () => {
    expect(await factory.feeAmountTickSpacing(FeeAmount.LOW)).to.eq(TICK_SPACINGS[FeeAmount.LOW])
    expect(await factory.feeAmountTickSpacing(FeeAmount.MEDIUM)).to.eq(TICK_SPACINGS[FeeAmount.MEDIUM])
    expect(await factory.feeAmountTickSpacing(FeeAmount.HIGH)).to.eq(TICK_SPACINGS[FeeAmount.HIGH])
  })

  async function createAndCheckPool(
    projectManager: string,
    tokens: [string, string],
    feeAmount: number,
    tickSpacing: number = TICK_SPACINGS[feeAmount as FeeAmount],
    launchParamsOverride?: Partial<Parameters<typeof factory.createPool>[3]>
  ) {
    // Grant POOL_MANAGER_ROLE to the project manager
    const POOL_MANAGER_ROLE = await factory.POOL_MANAGER_ROLE()
    await factory.grantRole(POOL_MANAGER_ROLE, projectManager)

    // Set up oracle registry with non-zero token based on the first token in the pair,
    const tokenForOracle = tokens[0] < tokens[1] ? tokens[0] : tokens[1] // Typically, the smaller address is token0
    // A generic oracle setup, specific stablecoin setup will be done in ETP tests

    if (await oracleRegistry.tokenOracles(tokenForOracle) === constants.AddressZero) {
        await factory.setOracle(tokenForOracle, wallet.address) // dummy oracle
    }
    if (await oracleRegistry.tokenOracles(tokens[0]) === constants.AddressZero) {
        await factory.setOracle(tokens[0], wallet.address) 
    }
     if (await oracleRegistry.tokenOracles(tokens[1]) === constants.AddressZero) {
        await factory.setOracle(tokens[1], wallet.address)
    }

    const defaultLaunchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: 0, // Factory will set to 0
      exclusiveTradingPeriodEnd: 0, // Factory will set to 0
      extendedLiquidityLockDuration: 0,
      projectManager: projectManager, // Use the projectManager passed to this function
      whitelistedAddressForSwap: wallet.address,
    }

    const launchParams = { ...defaultLaunchParams, ...launchParamsOverride }
    const tx = await factory.createPool(tokens[0], tokens[1], feeAmount, launchParams)

    const receipt = await tx.wait()
    const poolCreatedEvent = receipt.events?.find((e: any) => e.event === 'PoolCreated')
    expect(poolCreatedEvent).to.not.be.undefined
    const actualPoolAddress = poolCreatedEvent?.args?.pool
    expect(actualPoolAddress).to.be.properAddress

    const [expectedToken0, expectedToken1] =
      tokens[0].toLowerCase() < tokens[1].toLowerCase() ? tokens : [tokens[1], tokens[0]]

    await expect(tx)
      .to.emit(factory, 'PoolCreated')
      .withArgs(expectedToken0, expectedToken1, feeAmount, tickSpacing, actualPoolAddress, [
        launchParams.tokenLaunchType,
        launchParams.exclusiveTradingPeriodStart,
        launchParams.exclusiveTradingPeriodEnd,
        launchParams.extendedLiquidityLockDuration,
        launchParams.projectManager,
        launchParams.whitelistedAddressForSwap,
      ])

    await expect(factory.createPool(tokens[0], tokens[1], feeAmount, launchParams)).to.be.reverted
    await expect(factory.createPool(tokens[1], tokens[0], feeAmount, launchParams)).to.be.reverted
    expect(await factory.getPool(tokens[0], tokens[1], feeAmount), 'getPool in order').to.eq(actualPoolAddress)
    expect(await factory.getPool(tokens[1], tokens[0], feeAmount), 'getPool in reverse').to.eq(actualPoolAddress)

    const pool = (await ethers.getContractAt('IUniswapV3Pool', actualPoolAddress)) as IUniswapV3Pool
    expect(await pool.factory(), 'pool factory address').to.eq(factory.address)
    expect(await pool.token0(), 'pool token0').to.eq(expectedToken0)
    expect(await pool.token1(), 'pool token1').to.eq(expectedToken1)
    expect(await pool.fee(), 'pool fee').to.eq(feeAmount)
    expect(await pool.tickSpacing(), 'pool tick spacing').to.eq(tickSpacing)
  }

  describe('#createPool', () => {
    it('succeeds for low fee pool', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)
      await createAndCheckPool(TEST_PROJECT_MANAGER, TEST_ADDRESSES, FeeAmount.LOW)
    })

    it('succeeds for medium fee pool', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)
      await createAndCheckPool(TEST_PROJECT_MANAGER, TEST_ADDRESSES, FeeAmount.MEDIUM)
    })

    it('succeeds for high fee pool', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)
      await createAndCheckPool(TEST_PROJECT_MANAGER, TEST_ADDRESSES, FeeAmount.HIGH)
    })

    it('succeeds if tokens are passed in reverse', async () => {
      // Set oracle for the token that will be token0 (the smaller address)

      const [token0, token1] = TEST_ADDRESSES[1] < TEST_ADDRESSES[0] 
        ? [TEST_ADDRESSES[1], TEST_ADDRESSES[0]]
        : [TEST_ADDRESSES[0], TEST_ADDRESSES[1]]
      await factory.setOracle(token0, wallet.address)
      await createAndCheckPool(TEST_PROJECT_MANAGER, [TEST_ADDRESSES[0], TEST_ADDRESSES[1]], FeeAmount.MEDIUM)
    })

    it('fails if token a == token b', async () => {
      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: TEST_PROJECT_MANAGER,
        whitelistedAddressForSwap: wallet.address,
      }
      await expect(
        factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[0], FeeAmount.MEDIUM, launchParams)
      ).to.be.revertedWith('SO')
    })

    it('fails if token a is 0 or token b is 0', async () => {
      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: TEST_PROJECT_MANAGER,

        whitelistedAddressForSwap: wallet.address
      };
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)
      await factory.setOracle(TEST_ADDRESSES[1], wallet.address)

      await expect(factory.createPool(constants.AddressZero, TEST_ADDRESSES[1], FeeAmount.MEDIUM, launchParams)).to.be
        .reverted
      await expect(factory.createPool(TEST_ADDRESSES[0], constants.AddressZero, FeeAmount.MEDIUM, launchParams)).to.be
        .reverted
    })

    it('fails if fee amount is not enabled', async () => {
      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: TEST_PROJECT_MANAGER,
        whitelistedAddressForSwap: wallet.address
      };
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)
      await expect(factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], 100, launchParams)).to.be.revertedWith('FNT')
    })

    it('fails if oracle is not set', async () => {
      // Do NOT set any oracles for this test
      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: TEST_PROJECT_MANAGER,
        whitelistedAddressForSwap: wallet.address,
      }
      await expect(
        factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.MEDIUM, launchParams)
      ).to.be.revertedWith('ONS')
    })

    it('fails if vault is not set', async () => {
      // Create a new factory without vault set
      const FactoryFactory = await ethers.getContractFactory('UniswapV3Factory')
      const factoryWithoutVault = (await FactoryFactory.deploy()) as UniswapV3Factory
      await factoryWithoutVault.deployed()

      const OracleRegistryFactory = await ethers.getContractFactory('OracleRegistry')
      const oracleRegistryWithoutVault = (await OracleRegistryFactory.deploy(factoryWithoutVault.address)) as OracleRegistry
      await oracleRegistryWithoutVault.deployed()

      const PoolLogicFactory = await ethers.getContractFactory('UniswapV3Pool')
      const poolLogicWithoutVault = (await PoolLogicFactory.deploy()) as UniswapV3Pool
      await poolLogicWithoutVault.deployed()

      await factoryWithoutVault.initialize(wallet.address, oracleRegistryWithoutVault.address, poolLogicWithoutVault.address)
      await factoryWithoutVault.setPeriphery(wallet.address)

      // Grant POOL_MANAGER_ROLE to wallet
      const POOL_MANAGER_ROLE = await factoryWithoutVault.POOL_MANAGER_ROLE()
      await factoryWithoutVault.grantRole(POOL_MANAGER_ROLE, wallet.address)

      // Set oracles via the factory (not directly on the registry)
      await factoryWithoutVault.setOracle(TEST_ADDRESSES[0], wallet.address)
      await factoryWithoutVault.setOracle(TEST_ADDRESSES[1], wallet.address)

      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: TEST_PROJECT_MANAGER,
        whitelistedAddressForSwap: wallet.address
      }
      await expect(factoryWithoutVault.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.MEDIUM, launchParams)).to.be.revertedWith('VNS')
    })
  })

  describe('#setVault', () => {
    it('fails if caller is not owner', async () => {
      await expect(factory.connect(other).setVault(mockVault.address)).to.be.revertedWith('Unauthorized')
    })

    it('allows owner to set vault address', async () => {
      const newMockVaultFactory = await ethers.getContractFactory('MockVault')
      const newMockVault = (await newMockVaultFactory.deploy()) as MockVault
      await newMockVault.deployed()

      await factory.setVault(newMockVault.address)
      expect(await factory.vault()).to.eq(newMockVault.address)
    })

    it('emits VaultChanged event', async () => {
      const newMockVaultFactory = await ethers.getContractFactory('MockVault')
      const newMockVault = (await newMockVaultFactory.deploy()) as MockVault
      await newMockVault.deployed()

      const oldVault = await factory.vault()
      await expect(factory.setVault(newMockVault.address))
        .to.emit(factory, 'VaultChanged')
        .withArgs(oldVault, newMockVault.address)
    })

    it('allows setting vault to zero address', async () => {
      await factory.setVault(constants.AddressZero)
      expect(await factory.vault()).to.eq(constants.AddressZero)
    })
  })

  describe('#vault integration', () => {
    it('calls vault on successful pool creation', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)

      const launchParams = {
        tokenLaunchType: 1, // Different launch type for testing
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 2000,
        extendedLiquidityLockDuration: 100,
        projectManager: TEST_PROJECT_MANAGER,
        whitelistedAddressForSwap: wallet.address,
      }

      const tx = await factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.HIGH, launchParams)
      const receipt = await tx.wait()

      // Check that vault was called
      expect(await mockVault.callCount()).to.eq(1)

      // Get the pool address from the event
      const poolCreatedEvent = receipt.events?.find((e: any) => e.event === 'PoolCreated')
      const poolAddress = poolCreatedEvent?.args?.pool

      // Verify the data passed to vault
      const lastCallData = await mockVault.lastCallData()
      expect(lastCallData.poolAddress.toLowerCase()).to.eq(ethers.utils.hexZeroPad(poolAddress, 32).toLowerCase())

      const [expectedToken0, expectedToken1] =
        TEST_ADDRESSES[0].toLowerCase() < TEST_ADDRESSES[1].toLowerCase()
          ? [TEST_ADDRESSES[0], TEST_ADDRESSES[1]]
          : [TEST_ADDRESSES[1], TEST_ADDRESSES[0]]

      expect(lastCallData.token0.toLowerCase()).to.eq(ethers.utils.hexZeroPad(expectedToken0, 32).toLowerCase())
      expect(lastCallData.token1.toLowerCase()).to.eq(ethers.utils.hexZeroPad(expectedToken1, 32).toLowerCase())
      expect(lastCallData.launchType).to.eq(1)
      expect(lastCallData.projectLiquidityProvider).to.eq(ethers.utils.hexZeroPad(TEST_PROJECT_MANAGER, 32))
      expect(lastCallData.feeTier).to.eq(FeeAmount.HIGH)
      expect(lastCallData.exclusiveTradingPeriodStartTimestamp).to.eq(TEST_POOL_START_TIME)
      expect(lastCallData.exclusiveTradingPeriodEndTimestamp).to.eq(TEST_POOL_START_TIME + 2000)
    })

    it('emits VaultCalled event from mock vault', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)

      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: TEST_PROJECT_MANAGER,
        whitelistedAddressForSwap: wallet.address,
      }

      const tx = await factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.MEDIUM, launchParams)

      // Check that the vault emitted its event
      await expect(tx).to.emit(mockVault, 'VaultCalled')
    })

    it('reverts pool creation if vault call fails', async () => {
      // Configure mock vault to fail
      await mockVault.setShouldSucceed(false)

      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)

      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: TEST_PROJECT_MANAGER,
        whitelistedAddressForSwap: wallet.address,
      }

      await expect(
        factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.MEDIUM, launchParams)
      ).to.be.revertedWith('VCF')

      // Reset vault for other tests
      await mockVault.setShouldSucceed(true)
    })

    it('includes correct chain ID in vault call', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address)

      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: TEST_PROJECT_MANAGER,
        whitelistedAddressForSwap: wallet.address,
      }

      await factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.LOW, launchParams)

      const lastCallData = await mockVault.lastCallData()
      // Chain ID should be set (hardhat network typically uses 31337)
      expect(lastCallData.chainId).to.be.gt(0)
    })
  })

  describe('#setOwner', () => {
    it('fails if caller is not owner', async () => {
      await expect(factory.connect(other).grantRole(constants.HashZero, wallet.address)).to.be.reverted
    })

    it('updates owner', async () => {
      await factory.grantRole(constants.HashZero, other.address)
      await factory.revokeRole(constants.HashZero, wallet.address)
      expect(await factory.hasRole(constants.HashZero, other.address)).to.be.true
      expect(await factory.hasRole(constants.HashZero, wallet.address)).to.be.false
    })

    it('emits event', async () => {
      await expect(factory.grantRole(constants.HashZero, other.address))
        .to.emit(factory, 'RoleGranted')
        .withArgs(constants.HashZero, other.address, wallet.address)
    })

    it('cannot be called by original owner', async () => {
      await factory.grantRole(constants.HashZero, other.address)
      await factory.revokeRole(constants.HashZero, wallet.address)
      await expect(factory.grantRole(constants.HashZero, wallet.address)).to.be.reverted
    })
  })

  describe('#enableFeeAmount', () => {
    it('fails if caller is not owner', async () => {
      await expect(factory.connect(other).enableFeeAmount(100, 2)).to.be.reverted
    })
    it('fails if fee is too great', async () => {
      await expect(factory.enableFeeAmount(1000000, 10)).to.be.reverted
    })
    it('fails if tick spacing is too small', async () => {
      await expect(factory.enableFeeAmount(500, 0)).to.be.reverted
    })
    it('fails if tick spacing is too large', async () => {
      await expect(factory.enableFeeAmount(500, 16834)).to.be.reverted
    })
    it('fails if already initialized', async () => {
      await factory.enableFeeAmount(100, 5)
      await expect(factory.enableFeeAmount(100, 10)).to.be.reverted
    })
    it('sets the fee amount in the mapping', async () => {
      await factory.enableFeeAmount(100, 5)
      expect(await factory.feeAmountTickSpacing(100)).to.eq(5)
    })
    it('emits an event', async () => {
      await expect(factory.enableFeeAmount(100, 5)).to.emit(factory, 'FeeAmountEnabled').withArgs(100, 5)
    })
    it('enables pool creation', async () => {
      const launchParams = {
        tokenLaunchType: 0, // Assuming FairLaunch is 0, adjust if necessary
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME, // Replace with actual start time
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000, // Replace with actual end time
        extendedLiquidityLockDuration: 0, // Example value
        projectManager: TEST_PROJECT_MANAGER, // Example value, ensure this address has POOL_MANAGER_ROLE
        whitelistedAddressForSwap: wallet.address,
      }
      await factory.enableFeeAmount(200, 5) // Enable a new fee/tickSpacing
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address) // Ensure oracle is set for the token
      await factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], 200, launchParams)
    })
  })

  describe('#setPoolLogicContract', () => {
    let poolV2Logic: TestUniswapV3PoolV2

    beforeEach('deploy V2 pool logic', async () => {
      const PoolV2LogicFactory = await ethers.getContractFactory('TestUniswapV3PoolV2')
      poolV2Logic = (await PoolV2LogicFactory.connect(wallet).deploy()) as TestUniswapV3PoolV2
      await poolV2Logic.deployed()
    })

    it('fails if caller is not owner (OC)', async () => {
      await expect(factory.connect(other).setPoolLogicContract(poolV2Logic.address)).to.be.revertedWith('Unauthorized')
    })

    it('fails if new logic address is zero (ZLA)', async () => {
      await expect(factory.connect(wallet).setPoolLogicContract(constants.AddressZero)).to.be.revertedWith('ZLA')
    })

    it('updates the poolLogicContract address in factory state', async () => {
      await factory.connect(wallet).setPoolLogicContract(poolV2Logic.address)
      expect(await factory.poolLogicContract()).to.eq(poolV2Logic.address)
    })

    it('updates the beacon implementation address', async () => {
      const beaconAddress = await factory.poolBeacon()
      const beacon = await ethers.getContractAt('UpgradeableBeacon', beaconAddress, wallet)
      expect(await beacon.implementation()).to.eq(initialPoolLogic.address)
      await factory.connect(wallet).setPoolLogicContract(poolV2Logic.address)
      expect(await beacon.implementation()).to.eq(poolV2Logic.address)
    })

    it('emits PoolLogicContractChanged event', async () => {
      const oldLogicAddress = await factory.poolLogicContract()
      await expect(factory.connect(wallet).setPoolLogicContract(poolV2Logic.address))
        .to.emit(factory, 'PoolLogicContractChanged')
        .withArgs(oldLogicAddress, poolV2Logic.address)
    })

    it('newly created pool uses new V2 logic (has version() function)', async () => {
      await factory.connect(wallet).setPoolLogicContract(poolV2Logic.address)
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address) // Ensure oracle is set

      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: wallet.address,
        whitelistedAddressForSwap: wallet.address,
      }
      const tx = await factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.MEDIUM, launchParams)
      const receipt = await tx.wait()
      const poolCreatedEvent = receipt.events?.find((e: any) => e.event === 'PoolCreated')
      const newPoolAddress = poolCreatedEvent?.args?.pool
      expect(newPoolAddress).to.be.properAddress

      const newPool = (await ethers.getContractAt('TestUniswapV3PoolV2', newPoolAddress)) as TestUniswapV3PoolV2
      expect(await newPool.version()).to.equal('V2') // Changed from 2 to 'V2'
    })

    it('existing pool (beacon proxy) uses new V2 logic after upgrade', async () => {
      await factory.setOracle(TEST_ADDRESSES[0], wallet.address) // Ensure oracle is set FOR THE FIRST POOL

      const launchParamsOld = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: wallet.address,
        whitelistedAddressForSwap: wallet.address,
      }
      const txOld = await factory.createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], FeeAmount.LOW, launchParamsOld)
      const receiptOld = await txOld.wait()
      const poolCreatedEventOld = receiptOld.events?.find((e: any) => e.event === 'PoolCreated')
      const existingPoolAddress = poolCreatedEventOld?.args?.pool
      expect(existingPoolAddress).to.be.properAddress

      const poolAsV2BeforeUpgrade = (await ethers.getContractAt(
        'TestUniswapV3PoolV2',
        existingPoolAddress,
        wallet
      )) as TestUniswapV3PoolV2
      await expect(poolAsV2BeforeUpgrade.version()).to.be.reverted

      await factory.connect(wallet).setPoolLogicContract(poolV2Logic.address)

      const existingPool = (await ethers.getContractAt(
        'TestUniswapV3PoolV2',
        existingPoolAddress
      )) as TestUniswapV3PoolV2
      expect(await existingPool.version()).to.equal('V2') // Changed from 2 to 'V2'
      expect(await existingPool.factory()).to.eq(factory.address)
    })

    it('upgrades multiple existing pools (beacon proxies) to new V2 logic with a single beacon update', async () => {
      const pool1TokenA = TEST_ADDRESSES[0]
      const pool1TokenB = '******************************************'
      const pool2TokenA = TEST_ADDRESSES[1]
      const pool2TokenB = '******************************************'

      // Ensure oracles are set for all tokens involved
      await factory.setOracle(pool1TokenA, wallet.address)
      await factory.setOracle(pool1TokenB, wallet.address)
      await factory.setOracle(pool2TokenA, wallet.address)
      await factory.setOracle(pool2TokenB, wallet.address)

      const launchParams1 = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: wallet.address,
        whitelistedAddressForSwap: wallet.address,
      }
      const tx1 = await factory.createPool(pool1TokenA, pool1TokenB, FeeAmount.HIGH, launchParams1)
      const receipt1 = await tx1.wait()
      const event1 = receipt1.events?.find((e: any) => e.event === 'PoolCreated')
      const pool1Address = event1?.args?.pool
      expect(pool1Address).to.be.properAddress
      const pool1AsV2BeforeUpgrade = (await ethers.getContractAt(
        'TestUniswapV3PoolV2',
        pool1Address,
        wallet
      )) as TestUniswapV3PoolV2
      await expect(pool1AsV2BeforeUpgrade.version()).to.be.reverted

      const launchParams2 = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: wallet.address,
        whitelistedAddressForSwap: wallet.address,
      }
      const tx2 = await factory.createPool(pool2TokenA, pool2TokenB, FeeAmount.LOW, launchParams2)
      const receipt2 = await tx2.wait()
      const event2 = receipt2.events?.find((e: any) => e.event === 'PoolCreated')
      const pool2Address = event2?.args?.pool
      expect(pool2Address).to.be.properAddress
      const pool2AsV2BeforeUpgrade = (await ethers.getContractAt(
        'TestUniswapV3PoolV2',
        pool2Address,
        wallet
      )) as TestUniswapV3PoolV2
      await expect(pool2AsV2BeforeUpgrade.version()).to.be.reverted

      await factory.connect(wallet).setPoolLogicContract(poolV2Logic.address)

      const pool1 = (await ethers.getContractAt('TestUniswapV3PoolV2', pool1Address)) as TestUniswapV3PoolV2
      const pool2 = (await ethers.getContractAt('TestUniswapV3PoolV2', pool2Address)) as TestUniswapV3PoolV2
      expect(await pool1.version()).to.equal('V2') // Changed from 2 to 'V2'
      expect(await pool1.factory()).to.eq(factory.address)
      expect(await pool2.version()).to.equal('V2') // Changed from 2 to 'V2'
      expect(await pool2.factory()).to.eq(factory.address)
    })
  })

  describe('UniswapV3Factory Upgradeability (Transparent Proxy)', () => {
    let factory: UniswapV3Factory
    let oracleRegistry: OracleRegistry
    let initialPoolLogic: UniswapV3Pool
    let proxyAdminAddress: string
    let factoryV1ImplementationAddress: string

    beforeEach('deploy factory V1 as proxy', async () => {
      const OracleRegistryFactory = await ethers.getContractFactory('OracleRegistry')
      oracleRegistry = (await OracleRegistryFactory.deploy(wallet.address)) as OracleRegistry
      await oracleRegistry.deployed()

      const PoolLogicFactory = await ethers.getContractFactory('UniswapV3Pool')
      initialPoolLogic = (await PoolLogicFactory.deploy()) as UniswapV3Pool
      await initialPoolLogic.deployed()

      const UniswapV3FactoryFactory = await ethers.getContractFactory('UniswapV3Factory')
      factory = (await upgrades.deployProxy(
        UniswapV3FactoryFactory,
        [wallet.address, oracleRegistry.address, initialPoolLogic.address],
        { initializer: 'initialize', kind: 'transparent' }
      )) as UniswapV3Factory
      await factory.deployed()

      // Deploy and set up vault for upgradeability tests
      const MockVaultFactory = await ethers.getContractFactory('MockVault')
      const mockVault = (await MockVaultFactory.deploy()) as MockVault
      await mockVault.deployed()
      await factory.setVault(mockVault.address)

      // Set periphery for testing
      await factory.setPeriphery(wallet.address)

      // Assign these after factory is deployed
      factoryV1ImplementationAddress = await upgrades.erc1967.getImplementationAddress(factory.address)
      proxyAdminAddress = await upgrades.erc1967.getAdminAddress(factory.address)

      // Grant POOL_MANAGER_ROLE to all test signers on the proxy
      const signers = await ethers.getSigners()
      const POOL_MANAGER_ROLE = await factory.POOL_MANAGER_ROLE()
      for (const signer of signers) {
        await factory.grantRole(POOL_MANAGER_ROLE, signer.address)
      }

      await factory.setPeriphery(wallet.address)
    })

    it('should deploy V1 factory as a proxy with correct initial owner and implementation', async () => {
      expect(factory.address).to.be.properAddress
      // The owner is set via DEFAULT_ADMIN_ROLE in initialize, then OWNER_ROLE is granted.
      // AccessControl.owner() is not standard. hasRole(OWNER_ROLE, wallet.address) is more appropriate.
      // However, if the contract *does* have an owner() view returning initialOwner, that's fine.
      // Assuming it has an owner() view based on original test `factoryProxy.owner()`
      // Let's check how owner is typically fetched in other tests for this contract.
      // The first test in the file is: expect(await factory.hasRole(constants.HashZero, wallet.address)).to.be.true
      // constants.HashZero is DEFAULT_ADMIN_ROLE. The OWNER_ROLE is a custom role.
      // The contract does not have an `owner()` getter. It uses `hasRole(OWNER_ROLE, address)`.
      expect(await factory.hasRole(await factory.OWNER_ROLE(), wallet.address)).to.be.true
      expect(factoryV1ImplementationAddress).to.be.properAddress
      expect(proxyAdminAddress).to.be.properAddress
      expect(await factory.oracleRegistry()).to.equal(oracleRegistry.address)
      expect(await factory.poolLogicContract()).to.equal(initialPoolLogic.address)
      expect(await factory.feeAmountTickSpacing(FeeAmount.MEDIUM)).to.eq(TICK_SPACINGS[FeeAmount.MEDIUM])
    })

    it.skip('should allow V1 operations, upgrade to V2, and preserve state', async () => {
      const newOwnerAddress = other.address
      // To change owner, grant OWNER_ROLE to newOwnerAddress and revoke from wallet.address
      await factory.connect(wallet).grantRole(await factory.OWNER_ROLE(), newOwnerAddress)
      await factory.connect(wallet).revokeRole(await factory.OWNER_ROLE(), wallet.address)
      expect(await factory.hasRole(await factory.OWNER_ROLE(), newOwnerAddress)).to.be.true

      const customFee = 2000
      const customTickSpacing = 40
      // enableFeeAmount requires OWNER_ROLE, which `other` now has.
      await factory.connect(other).enableFeeAmount(customFee, customTickSpacing)
      expect(await factory.feeAmountTickSpacing(customFee)).to.equal(customTickSpacing)

      const FactoryV2 = await ethers.getContractFactory('TestUniswapV3FactoryV2', wallet)
      const factoryV2Proxy = (await upgrades.upgradeProxy(factory.address, FactoryV2)) as TestUniswapV3FactoryV2
      await factoryV2Proxy.deployed()

      const factoryV2ImplementationAddress = await upgrades.erc1967.getImplementationAddress(factoryV2Proxy.address)
      expect(factoryV2ImplementationAddress).to.not.equal(factoryV1ImplementationAddress)
      expect(factoryV2Proxy.address).to.equal(factory.address)

      expect(await factoryV2Proxy.hasRole(await factoryV2Proxy.OWNER_ROLE(), newOwnerAddress)).to.be.true
      expect(await factoryV2Proxy.feeAmountTickSpacing(customFee)).to.equal(customTickSpacing)
      expect(await factoryV2Proxy.feeAmountTickSpacing(FeeAmount.MEDIUM)).to.eq(TICK_SPACINGS[FeeAmount.MEDIUM])
      expect(await factoryV2Proxy.oracleRegistry()).to.equal(oracleRegistry.address)
      expect(await factoryV2Proxy.poolLogicContract()).to.equal(initialPoolLogic.address)

      // Grant OWNER_ROLE back to wallet for setPeriphery, using the new owner (other)
      await factoryV2Proxy.connect(other).grantRole(await factoryV2Proxy.OWNER_ROLE(), wallet.address)
      await factoryV2Proxy.connect(wallet).setPeriphery(wallet.address)
    })

    it('should allow V2 specific functions after upgrade and V2 initialization', async () => {
      const FactoryV2 = await ethers.getContractFactory('TestUniswapV3FactoryV2', wallet)
      const factoryV2Proxy = (await upgrades.upgradeProxy(factory.address, FactoryV2)) as TestUniswapV3FactoryV2
      await factoryV2Proxy.deployed()

      expect(await factoryV2Proxy.isV2()).to.be.true // Assuming TestUniswapV3FactoryV2 has an isV2() getter

      const versionToSet = 200
      // initializeV2 should be callable by the owner (wallet, who is DEFAULT_ADMIN and still has it if not explicitly revoked)
      // or by `other` if OWNER_ROLE was transferred and initializeV2 is onlyRole(OWNER_ROLE)
      // Assuming initializeV2 requires OWNER_ROLE and owner was changed to `other` in previous test logic,
      // but tests are independent. So, `wallet` should still have OWNER_ROLE here.
      await expect(factoryV2Proxy.connect(wallet).initializeV2(versionToSet))
        .to.emit(factoryV2Proxy, 'FactoryUpgradedToV2')
        .withArgs(versionToSet)

      expect(await factoryV2Proxy.getFactoryVersionNumber()).to.equal(versionToSet)

      await expect(factoryV2Proxy.connect(wallet).initializeV2(versionToSet + 1)).to.be.revertedWith(
        'TestUniswapV3FactoryV2: already initialized V2' // Or the specific revert message from TestUniswapV3FactoryV2
      )
    })

    it.skip('should allow creating pools via the upgraded V2 factory', async () => {
      const FactoryV2 = await ethers.getContractFactory('TestUniswapV3FactoryV2', wallet)
      const factoryV2Proxy = (await upgrades.upgradeProxy(factory.address, FactoryV2)) as TestUniswapV3FactoryV2
      await factoryV2Proxy.deployed()

      // Grant POOL_MANAGER_ROLE to all test signers on the upgraded proxy
      const signersV2 = await ethers.getSigners()
      const POOL_MANAGER_ROLE_V2 = await factoryV2Proxy.POOL_MANAGER_ROLE()
      for (const signer of signersV2) {
        await factoryV2Proxy.connect(wallet).grantRole(POOL_MANAGER_ROLE_V2, signer.address)
      }

      // Ensure oracle is set. `oracleRegistry` is the deployed OracleRegistry instance.
      // Update OracleRegistry's factory address to point to the new proxy
      await oracleRegistry.setFactory(factoryV2Proxy.address)
      await factoryV2Proxy.connect(wallet).setOracle(TEST_ADDRESSES[0], wallet.address)

      const feeAmount = FeeAmount.LOW
      const tickSpacing = TICK_SPACINGS[FeeAmount.LOW]

      const launchParams = {
        tokenLaunchType: 0, // Assuming FairLaunch is 0, adjust if necessary
        exclusiveTradingPeriodStart: TEST_POOL_START_TIME,
        exclusiveTradingPeriodEnd: TEST_POOL_START_TIME + 1000,
        extendedLiquidityLockDuration: 0,
        projectManager: wallet.address,
        whitelistedAddressForSwap: wallet.address,
      }

      // createPool now takes launchParams
      const createPoolTx = await factoryV2Proxy
        .connect(wallet)
        .createPool(TEST_ADDRESSES[0], TEST_ADDRESSES[1], feeAmount, launchParams)
      const receipt = await createPoolTx.wait()
      const poolCreatedEvent = receipt.events?.find((e: any) => e.event === 'PoolCreated')
      const poolAddress = poolCreatedEvent?.args?.pool

      expect(poolAddress).to.be.properAddress

      const [expectedToken0, expectedToken1] =
        TEST_ADDRESSES[0].toLowerCase() < TEST_ADDRESSES[1].toLowerCase()
          ? [TEST_ADDRESSES[0], TEST_ADDRESSES[1]]
          : [TEST_ADDRESSES[1], TEST_ADDRESSES[0]]

      await expect(createPoolTx)
        .to.emit(factoryV2Proxy, 'PoolCreated')
        .withArgs(expectedToken0, expectedToken1, feeAmount, tickSpacing, poolAddress, [
          launchParams.tokenLaunchType,
          launchParams.exclusiveTradingPeriodStart,
          launchParams.exclusiveTradingPeriodEnd,
          launchParams.extendedLiquidityLockDuration,
          launchParams.projectManager,
          launchParams.whitelistedAddressForSwap,
        ])

      const pool = (await ethers.getContractAt('IUniswapV3Pool', poolAddress, wallet)) as IUniswapV3Pool
      expect(await pool.factory()).to.equal(factoryV2Proxy.address)
      expect(await pool.token0()).to.equal(expectedToken0)
      expect(await pool.token1()).to.equal(expectedToken1)
      expect(await pool.fee()).to.equal(feeAmount)
      expect(await pool.tickSpacing()).to.equal(tickSpacing)

      // Grant OWNER_ROLE back to wallet for setPeriphery, using the new owner (other)
      await factoryV2Proxy.connect(other).grantRole(await factoryV2Proxy.OWNER_ROLE(), wallet.address)
      await factoryV2Proxy.connect(wallet).setPeriphery(wallet.address)
    })
  })
})
