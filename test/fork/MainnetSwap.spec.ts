import { ethers, upgrades, network } from "hardhat";
import { expect } from "chai";
import { Contract } from "ethers";


describe.skip("USDC/LCoin Pool on Mainnet Fork", function () {
  let deployer: any;
  let usdc: Contract;
  let lcoin: Contract;
  let factory: Contract;
  let oracleRegistry: Contract;
  let poolLogic: Contract;
  let pool: Contract;

  before(async () => {
    [deployer] = await ethers.getSigners();

    // Deploy test USDC (6 decimals) and LCoin (18 decimals)
    const USDC = await ethers.getContractFactory("TestERC20");
    usdc = await USDC.deploy(ethers.utils.parseUnits("1000000000", 6), 6);
    await usdc.deployed();

    const LCoin = await ethers.getContractFactory("TestERC20");
    lcoin = await LCoin.deploy(ethers.utils.parseUnits("1000000000", 18), 18);
    await lcoin.deployed();

    // Grant POOL_MANAGER_ROLE to deployer
    const POOL_MANAGER_ROLE = await factory.POOL_MANAGER_ROLE()
    await factory.grantRole(POOL_MANAGER_ROLE, deployer.address)
  });

  it("deploys contracts, creates pool, funds with 200K USDC, and swaps", async () => {
    // Deploy UniswapV3Pool logic
    const UniswapV3Pool = await ethers.getContractFactory("UniswapV3Pool");
    poolLogic = await UniswapV3Pool.deploy();
    await poolLogic.deployed();

    // Deploy UniswapV3Factory (Upgradeable)
    const UniswapV3Factory = await ethers.getContractFactory("UniswapV3Factory");
    factory = await upgrades.deployProxy(
      UniswapV3Factory,
      [
        deployer.address, 
        deployer.address, 
        ethers.constants.AddressZero, // temp oracleRegistry
        poolLogic.address
      ],
      { initializer: "initialize", kind: "transparent" }
    );
    await factory.deployed();

    // Deploy OracleRegistry
    const OracleRegistry = await ethers.getContractFactory("OracleRegistry");
    oracleRegistry = await OracleRegistry.deploy(factory.address);
    await oracleRegistry.deployed();
    // Set OracleRegistry in factory
    await factory.setOracleRegistry(oracleRegistry.address);

    // Deploy and set mock Chainlink oracles for USDC and LCoin
    const MockChainlinkOracle = await ethers.getContractFactory("MockChainlinkOracle");
    const mockOracleUsdc = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 USDC = 1 USD, 6 decimals
    await mockOracleUsdc.deployed();

    await factory.setOracle(usdc.address, mockOracleUsdc.address);
    const mockOracleLcoin = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 LCoin = 1 USD, 6 decimals
    await mockOracleLcoin.deployed();
    await factory.setOracle(lcoin.address, mockOracleLcoin.address);

    // Deploy callee contract for mint/swap callbacks
    const Callee = await ethers.getContractFactory("TestUniswapV3Callee");
    const callee = await Callee.deploy();
    await callee.deployed();

    // Create USDC/LCoin pool
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now - 10,
      exclusiveTradingPeriodEnd: now + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: callee.address,
      projectManager: deployer.address
    };

    const fee = 3000;
    const tx = await factory.createPool(usdc.address, lcoin.address, fee, launchParams);
    const receipt = await tx.wait();
    const poolCreatedEvent = receipt.events.find((e: any) => e.event === "PoolCreated");
    const poolAddress = poolCreatedEvent ? poolCreatedEvent.args.pool : null;
    expect(poolAddress).to.be.properAddress;
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);

    // Initialize the pool at 1:1 price (Q64.96 encoding)
    const initialSqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    await pool.initialize(initialSqrtPriceX96);

    // Fund deployer with tokens for minting and swap
    await usdc.mint(deployer.address, ethers.utils.parseUnits("2000", 6));
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("1000", 18));

    // Approve callee for both tokens before mint
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);


    // Use a tick range that includes the current price (0) and a large liquidity value
    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const liquidity = ethers.utils.parseUnits("200000", 6); // Large enough to require 200,000 USDC
    
    // Mint liquidity using callee
    await callee.mint(pool.address, deployer.address, tickLower, tickUpper, liquidity);

    // Approve callee for both tokens before swap
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Advance time to after ETP start
    await ethers.provider.send('evm_setNextBlockTimestamp', [now + 100]);
    await ethers.provider.send('evm_mine', []);

    // Perform a swap: swap 1000 USDC for LCoin using callee
    const usdcBalanceBefore = await usdc.balanceOf(deployer.address);
    const lcoinBalanceBefore = await lcoin.balanceOf(deployer.address);
    const MIN_SQRT_RATIO = ethers.BigNumber.from('**********');
    await callee.swapExact0For1(pool.address, ethers.utils.parseUnits("1000", 6), deployer.address, MIN_SQRT_RATIO.add(1));
    const usdcBalanceAfter = await usdc.balanceOf(deployer.address);
    const lcoinBalanceAfter = await lcoin.balanceOf(deployer.address);

    // Assert balances
    expect(usdcBalanceAfter.lt(usdcBalanceBefore)).to.be.true;
    expect(lcoinBalanceAfter.gt(lcoinBalanceBefore)).to.be.true;
  });

  it("should not allow swap if pool does not have enough 200K stablecoin liquidity (FairLaunch)", async () => {
    // Deploy UniswapV3Pool logic
    const UniswapV3Pool = await ethers.getContractFactory("UniswapV3Pool");
    poolLogic = await UniswapV3Pool.deploy();
    await poolLogic.deployed();

    // Deploy UniswapV3Factory (Upgradeable)
    const UniswapV3Factory = await ethers.getContractFactory("UniswapV3Factory");
    factory = await upgrades.deployProxy(
      UniswapV3Factory,
      [
        deployer.address, 
        deployer.address, 
        ethers.constants.AddressZero, // temp oracleRegistry
        poolLogic.address
      ],
      { initializer: "initialize", kind: "transparent" }
    );
    await factory.deployed();

    // Deploy OracleRegistry
    const OracleRegistry = await ethers.getContractFactory("OracleRegistry");
    oracleRegistry = await OracleRegistry.deploy(factory.address);
    await oracleRegistry.deployed();
    // Set OracleRegistry in factory
    await factory.setOracleRegistry(oracleRegistry.address);

    // Deploy and set mock Chainlink oracles for USDC and LCoin
    const MockChainlinkOracle = await ethers.getContractFactory("MockChainlinkOracle");
    const mockOracleUsdc = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 USDC = 1 USD, 6 decimals
    await mockOracleUsdc.deployed();
    await factory.setOracle(usdc.address, mockOracleUsdc.address);
    const mockOracleLcoin = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 LCoin = 1 USD, 6 decimals
    await mockOracleLcoin.deployed();
    await factory.setOracle(lcoin.address, mockOracleLcoin.address);

    // Deploy callee contract for mint/swap callbacks
    const Callee = await ethers.getContractFactory("TestUniswapV3Callee");
    const callee = await Callee.deploy();
    await callee.deployed();

    // Create USDC/LCoin pool
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now - 10,
      exclusiveTradingPeriodEnd: now + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: callee.address,
      projectManager: deployer.address
    };

    const fee = 3000;
    const tx = await factory.createPool(usdc.address, lcoin.address, fee, launchParams);
    const receipt = await tx.wait();
    const poolCreatedEvent = receipt.events.find((e: any) => e.event === "PoolCreated");
    const poolAddress = poolCreatedEvent ? poolCreatedEvent.args.pool : null;
    expect(poolAddress).to.be.properAddress;
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);

    // Initialize the pool at 1:1 price (Q64.96 encoding)
    const initialSqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    await pool.initialize(initialSqrtPriceX96);

    // Fund deployer with tokens for minting and swap (but NOT enough for 200K USDC in pool)
    await usdc.mint(deployer.address, ethers.utils.parseUnits("1000", 6));
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("1000", 18));

    // Approve callee for both tokens before mint
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Use a tick range that includes the current price (0) and a large liquidity value
    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const liquidity = ethers.utils.parseUnits("200000", 6); // Large enough to require 200,000 USDC
    // Mint liquidity using callee
    await callee.mint(pool.address, deployer.address, tickLower, tickUpper, liquidity);

    // Approve callee for both tokens before swap
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Advance time to after ETP start
    const latestBlock = await ethers.provider.getBlock('latest');
    const nextTimestamp = Math.max(now + 100, latestBlock.timestamp + 10);
    await ethers.provider.send('evm_setNextBlockTimestamp', [nextTimestamp]);
    await ethers.provider.send('evm_mine', []);

    // Log pool's USDC balance, required stablecoin liquidity, and oracle price before swap
    const poolUsdcBalance = await usdc.balanceOf(pool.address);
    const requiredStable = await pool.requiredStableCoinLiquidityFairLaunch();
    const oraclePrice = await oracleRegistry.getPrice(usdc.address);
    console.log('Pool USDC balance:', poolUsdcBalance.toString());
    console.log('Required stablecoin liquidity:', requiredStable.toString());
    console.log('Oracle price for USDC:', oraclePrice.toString());

    // Log required stablecoin liquidity before update
    const requiredStableBefore = await pool.requiredStableCoinLiquidityFairLaunch();
    console.log('Required stablecoin liquidity BEFORE update:', requiredStableBefore.toString());
    // Set required stablecoin liquidity to 200,000 USDC (6 decimals)
    await expect(
      pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.utils.parseUnits('200000', 6))
    ).to.not.be.reverted;
    const requiredStableAfter = await pool.requiredStableCoinLiquidityFairLaunch();
    console.log('Required stablecoin liquidity AFTER update:', requiredStableAfter.toString());

    // Try to swap 1000 USDC for LCoin using callee, should revert with 'T'
    const MIN_SQRT_RATIO = ethers.BigNumber.from('**********');
    await expect(
      callee.swapExact0For1(pool.address, ethers.utils.parseUnits("1000", 6), deployer.address, MIN_SQRT_RATIO.add(1))
    ).to.be.revertedWith('T');
  });

  it("should not allow swap if ETP has not started", async () => {
    // Deploy UniswapV3Pool logic
    const UniswapV3Pool = await ethers.getContractFactory("UniswapV3Pool");
    poolLogic = await UniswapV3Pool.deploy();
    await poolLogic.deployed();

    // Deploy UniswapV3Factory (Upgradeable)
    const UniswapV3Factory = await ethers.getContractFactory("UniswapV3Factory");
    factory = await upgrades.deployProxy(
      UniswapV3Factory,
      [
        deployer.address, 
        deployer.address, 
        ethers.constants.AddressZero, // temp oracleRegistry
        poolLogic.address
      ],
      { initializer: "initialize", kind: "transparent" }
    );
    await factory.deployed();

    // Deploy OracleRegistry
    const OracleRegistry = await ethers.getContractFactory("OracleRegistry");
    oracleRegistry = await OracleRegistry.deploy(factory.address);
    await oracleRegistry.deployed();
    // Set OracleRegistry in factory
    await factory.setOracleRegistry(oracleRegistry.address);

    // Deploy and set mock Chainlink oracles for USDC and LCoin
    const MockChainlinkOracle = await ethers.getContractFactory("MockChainlinkOracle");
    const mockOracleUsdc = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 USDC = 1 USD, 6 decimals
    await mockOracleUsdc.deployed();
    await factory.setOracle(usdc.address, mockOracleUsdc.address);
    const mockOracleLcoin = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 LCoin = 1 USD, 6 decimals
    await mockOracleLcoin.deployed();
    await factory.setOracle(lcoin.address, mockOracleLcoin.address);

    // Deploy callee contract for mint/swap callbacks
    const Callee = await ethers.getContractFactory("TestUniswapV3Callee");
    const callee = await Callee.deploy();
    await callee.deployed();

    // Create USDC/LCoin pool with ETP start in the future
    const now = Math.floor(Date.now() / 1000);
    const etpStart = now + 3600; // 1 hour in the future
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: etpStart,
      exclusiveTradingPeriodEnd: etpStart + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: callee.address,
      projectManager: deployer.address
    };

    const fee = 3000;
    const tx = await factory.createPool(usdc.address, lcoin.address, fee, launchParams);
    const receipt = await tx.wait();
    const poolCreatedEvent = receipt.events.find((e: any) => e.event === "PoolCreated");
    const poolAddress = poolCreatedEvent ? poolCreatedEvent.args.pool : null;
    expect(poolAddress).to.be.properAddress;
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);

    // Initialize the pool at 1:1 price (Q64.96 encoding)
    const initialSqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    await pool.initialize(initialSqrtPriceX96);

    // Fund deployer with tokens for minting and swap
    await usdc.mint(deployer.address, ethers.utils.parseUnits("200000", 6));
    await usdc.transfer(pool.address, ethers.utils.parseUnits("200000", 6));
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("1000", 18));

    // Approve callee for both tokens before mint
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Use a tick range that includes the current price (0) and a large liquidity value
    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const liquidity = ethers.utils.parseUnits("200000", 6); // Large enough to require 200,000 USDC
    // Mint liquidity using callee
    await callee.mint(pool.address, deployer.address, tickLower, tickUpper, liquidity);

    // Approve callee for both tokens before swap
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Set required stablecoin liquidity to 200,000 USDC (6 decimals)
    await pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.utils.parseUnits('200000', 6));

    // Do NOT advance time to ETP start (stay before ETP)

    // Try to swap 1000 USDC for LCoin using callee, should revert with 'T'
    const MIN_SQRT_RATIO = ethers.BigNumber.from('**********');
    await expect(
      callee.swapExact0For1(pool.address, ethers.utils.parseUnits("1000", 6), deployer.address, MIN_SQRT_RATIO.add(1))
    ).to.be.revertedWith('T');
  });

  it("should deduct correct fee during ETP: 1% total, 0.66% to LPs, 0.33% protocol", async () => {
    // Deploy UniswapV3Pool logic
    const UniswapV3Pool = await ethers.getContractFactory("UniswapV3Pool");
    poolLogic = await UniswapV3Pool.deploy();
    await poolLogic.deployed();

    // Deploy UniswapV3Factory (Upgradeable)
    const UniswapV3Factory = await ethers.getContractFactory("UniswapV3Factory");
    factory = await upgrades.deployProxy(
      UniswapV3Factory,
      [
        deployer.address, 
        deployer.address, 
        ethers.constants.AddressZero, // temp oracleRegistry
        poolLogic.address
      ],
      { initializer: "initialize", kind: "transparent" }
    );
    await factory.deployed();

    // Deploy OracleRegistry
    const OracleRegistry = await ethers.getContractFactory("OracleRegistry");
    oracleRegistry = await OracleRegistry.deploy(factory.address);
    await oracleRegistry.deployed();
    // Set OracleRegistry in factory
    await factory.setOracleRegistry(oracleRegistry.address);

    // Deploy and set mock Chainlink oracles for USDC and LCoin
    const MockChainlinkOracle = await ethers.getContractFactory("MockChainlinkOracle");
    const mockOracleUsdc = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 USDC = 1 USD, 6 decimals
    await mockOracleUsdc.deployed();
    await factory.setOracle(usdc.address, mockOracleUsdc.address);
    const mockOracleLcoin = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 LCoin = 1 USD, 6 decimals
    await mockOracleLcoin.deployed();
    await factory.setOracle(lcoin.address, mockOracleLcoin.address);

    // Deploy callee contract for mint/swap callbacks
    const Callee = await ethers.getContractFactory("TestUniswapV3Callee");
    const callee = await Callee.deploy();
    await callee.deployed();

    // Create USDC/LCoin pool with ETP active now
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now - 10,
      exclusiveTradingPeriodEnd: now + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: callee.address,
      projectManager: deployer.address
    };

    const fee = 10000; // 1% fee
    const tx = await factory.createPool(usdc.address, lcoin.address, fee, launchParams);
    const receipt = await tx.wait();
    const poolCreatedEvent = receipt.events.find((e: any) => e.event === "PoolCreated");
    const poolAddress = poolCreatedEvent ? poolCreatedEvent.args.pool : null;
    expect(poolAddress).to.be.properAddress;
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);

    // Initialize the pool at 1:1 price (Q64.96 encoding)
    const initialSqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    await pool.initialize(initialSqrtPriceX96);

    // Fund deployer with tokens for minting and swap
    await usdc.mint(deployer.address, ethers.utils.parseUnits("200000", 6));
    await usdc.transfer(pool.address, ethers.utils.parseUnits("200000", 6));
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("1000", 18));

    // Approve callee for both tokens before mint
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Use a tick range that includes the current price (0) and a large liquidity value
    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const liquidity = ethers.utils.parseUnits("200000", 6); // Large enough to require 200,000 USDC
    // Mint liquidity using callee
    await callee.mint(pool.address, deployer.address, tickLower, tickUpper, liquidity);

    // Approve callee for both tokens before swap
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Set required stablecoin liquidity to 200,000 USDC (6 decimals)
    await pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.utils.parseUnits('200000', 6));

    // Set protocol fee denominator to 3 (so 1/3 of fee goes to protocol, 2/3 to LPs)
    // This is set via setFeeProtocol(feeProtocol0, feeProtocol1) where denominator is 3
    await pool.setFeeProtocol(3, 3);

    // Advance time to after ETP start
    const latestBlock = await ethers.provider.getBlock('latest');
    const nextTimestamp = Math.max(now + 100, latestBlock.timestamp + 10);
    await ethers.provider.send('evm_setNextBlockTimestamp', [nextTimestamp]);
    await ethers.provider.send('evm_mine', []);

    // Perform a swap: swap 1000 USDC for LCoin using callee
    const usdcBalanceBefore = await usdc.balanceOf(deployer.address);
    const lcoinBalanceBefore = await lcoin.balanceOf(deployer.address);
    const MIN_SQRT_RATIO = ethers.BigNumber.from('**********');
    await callee.swapExact0For1(pool.address, ethers.utils.parseUnits("1000", 6), deployer.address, MIN_SQRT_RATIO.add(1));
    const usdcBalanceAfter = await usdc.balanceOf(deployer.address);
    const lcoinBalanceAfter = await lcoin.balanceOf(deployer.address);

    // Burn (poke) and collect for LP
    await pool.burn(tickLower, tickUpper, 0); // poke to update fees
    const owed0 = (await pool.positions(
      ethers.utils.solidityKeccak256(["address","int24","int24"], [deployer.address, tickLower, tickUpper])
    )).tokensOwed0;
    const owed1 = (await pool.positions(
      ethers.utils.solidityKeccak256(["address","int24","int24"], [deployer.address, tickLower, tickUpper])
    )).tokensOwed1;
    const maxUint128 = ethers.BigNumber.from('0xffffffffffffffffffffffffffffffff');
    // Collect fees for LP
    await pool.collect(deployer.address, tickLower, tickUpper, maxUint128, maxUint128);

    // Check protocol fees
    const protocolFees = await pool.protocolFees();
    // Collect protocol fees as owner
    await pool.collectProtocol(deployer.address, maxUint128, maxUint128);

    // Assert fee split is roughly correct (allowing for rounding)
    // 1% of 1000 = 10, so ~6.66 to LP, ~3.33 to protocol
    expect(Number(owed0) + Number(protocolFees.token0)).to.be.closeTo(10 * 1e6, 2 * 1e6);
    expect(Number(owed0)).to.be.closeTo(7 * 1e6, 2 * 1e6);
    expect(Number(protocolFees.token0)).to.be.closeTo(3 * 1e6, 2 * 1e6);
    // Assert balances
    expect(usdcBalanceAfter.lt(usdcBalanceBefore)).to.be.true;
    expect(lcoinBalanceAfter.gt(lcoinBalanceBefore)).to.be.true;
  });

  it("should only allow owner to collect protocol fees", async () => {
    // Deploy UniswapV3Pool logic
    const UniswapV3Pool = await ethers.getContractFactory("UniswapV3Pool");
    poolLogic = await UniswapV3Pool.deploy();
    await poolLogic.deployed();

    // Deploy UniswapV3Factory (Upgradeable)
    const UniswapV3Factory = await ethers.getContractFactory("UniswapV3Factory");
    factory = await upgrades.deployProxy(
      UniswapV3Factory,
      [
        deployer.address, 
        deployer.address, 
        ethers.constants.AddressZero, // temp oracleRegistry
        poolLogic.address
      ],
      { initializer: "initialize", kind: "transparent" }
    );
    await factory.deployed();

    // Deploy OracleRegistry
    const OracleRegistry = await ethers.getContractFactory("OracleRegistry");
    oracleRegistry = await OracleRegistry.deploy(factory.address);
    await oracleRegistry.deployed();
    // Set OracleRegistry in factory
    await factory.setOracleRegistry(oracleRegistry.address);

    // Deploy and set mock Chainlink oracles for USDC and LCoin
    const MockChainlinkOracle = await ethers.getContractFactory("MockChainlinkOracle");
    const mockOracleUsdc = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 USDC = 1 USD, 6 decimals
    await mockOracleUsdc.deployed();
    await factory.setOracle(usdc.address, mockOracleUsdc.address);
    const mockOracleLcoin = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 LCoin = 1 USD, 6 decimals
    await mockOracleLcoin.deployed();
    await factory.setOracle(lcoin.address, mockOracleLcoin.address);

    // Deploy callee contract for mint/swap callbacks
    const Callee = await ethers.getContractFactory("TestUniswapV3Callee");
    const callee = await Callee.deploy();
    await callee.deployed();

    // Create USDC/LCoin pool with ETP active now
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now - 10,
      exclusiveTradingPeriodEnd: now + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: callee.address,
      projectManager: deployer.address
    };

    const fee = 10000; // 1% fee
    const tx = await factory.createPool(usdc.address, lcoin.address, fee, launchParams);
    const receipt = await tx.wait();
    const poolCreatedEvent = receipt.events.find((e: any) => e.event === "PoolCreated");
    const poolAddress = poolCreatedEvent ? poolCreatedEvent.args.pool : null;
    expect(poolAddress).to.be.properAddress;
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);

    // Initialize the pool at 1:1 price (Q64.96 encoding)
    const initialSqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    await pool.initialize(initialSqrtPriceX96);

    // Fund deployer with tokens for minting and swap
    await usdc.mint(deployer.address, ethers.utils.parseUnits("200000", 6));
    await usdc.transfer(pool.address, ethers.utils.parseUnits("200000", 6));
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("1000", 18));

    // Approve callee for both tokens before mint
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Use a tick range that includes the current price (0) and a reasonable liquidity value
    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const liquidity = 1000;
    // Mint liquidity using callee
    await callee.mint(pool.address, deployer.address, tickLower, tickUpper, liquidity);

    // Approve callee for both tokens before swap
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Set required stablecoin liquidity to 200,000 USDC (6 decimals)
    await pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.utils.parseUnits('200000', 6));

    // Set protocol fee denominator to 3 (so 1/3 of fee goes to protocol, 2/3 to LPs)
    await pool.setFeeProtocol(3, 3);

    // Advance time to after ETP start
    const latestBlock = await ethers.provider.getBlock('latest');
    const nextTimestamp = Math.max(now + 100, latestBlock.timestamp + 10);
    await ethers.provider.send('evm_setNextBlockTimestamp', [nextTimestamp]);
    await ethers.provider.send('evm_mine', []);

    // Perform a swap: swap 1000 USDC for LCoin using callee
    const MIN_SQRT_RATIO = ethers.BigNumber.from('**********');
    await callee.swapExact0For1(pool.address, ethers.utils.parseUnits("1000", 6), deployer.address, MIN_SQRT_RATIO.add(1));

    // Try to collect protocol fees as a non-owner
    const [_, nonOwner] = await ethers.getSigners();
    const maxUint128 = ethers.BigNumber.from('0xffffffffffffffffffffffffffffffff');
    await expect(
      pool.connect(nonOwner).collectProtocol(nonOwner.address, maxUint128, maxUint128)
    ).to.be.reverted;

    // Collect protocol fees as owner (should succeed)
    await expect(
      pool.collectProtocol(deployer.address, maxUint128, maxUint128)
    ).to.not.be.reverted;
  });

  it("happy path: add liquidity, remove liquidity, collect fees", async () => {
    // Deploy UniswapV3Pool logic
    const UniswapV3Pool = await ethers.getContractFactory("UniswapV3Pool");
    poolLogic = await UniswapV3Pool.deploy();
    await poolLogic.deployed();

    // Deploy UniswapV3Factory (Upgradeable)
    const UniswapV3Factory = await ethers.getContractFactory("UniswapV3Factory");
    factory = await upgrades.deployProxy(
      UniswapV3Factory,
      [
        deployer.address, 
        deployer.address, 
        ethers.constants.AddressZero, // temp oracleRegistry
        poolLogic.address
      ],
      { initializer: "initialize", kind: "transparent" }
    );
    await factory.deployed();

    // Deploy OracleRegistry
    const OracleRegistry = await ethers.getContractFactory("OracleRegistry");
    oracleRegistry = await OracleRegistry.deploy(factory.address);
    await oracleRegistry.deployed();
    // Set OracleRegistry in factory
    await factory.setOracleRegistry(oracleRegistry.address);

    // Deploy and set mock Chainlink oracles for USDC and LCoin
    const MockChainlinkOracle = await ethers.getContractFactory("MockChainlinkOracle");
    const mockOracleUsdc = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 USDC = 1 USD, 6 decimals
    await mockOracleUsdc.deployed();
    await factory.setOracle(usdc.address, mockOracleUsdc.address);
    const mockOracleLcoin = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 LCoin = 1 USD, 6 decimals
    await mockOracleLcoin.deployed();
    await factory.setOracle(lcoin.address, mockOracleLcoin.address);

    // Deploy callee contract for mint/swap callbacks
    const Callee = await ethers.getContractFactory("TestUniswapV3Callee");
    const callee = await Callee.deploy();
    await callee.deployed();

    // Create USDC/LCoin pool with ETP active now
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now - 10,
      exclusiveTradingPeriodEnd: now + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: callee.address,
      projectManager: deployer.address
    };

    const fee = 3000;
    const tx = await factory.createPool(usdc.address, lcoin.address, fee, launchParams);
    const receipt = await tx.wait();
    const poolCreatedEvent = receipt.events.find((e: any) => e.event === "PoolCreated");
    const poolAddress = poolCreatedEvent ? poolCreatedEvent.args.pool : null;
    expect(poolAddress).to.be.properAddress;
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);

    // Initialize the pool at 1:1 price (Q64.96 encoding)
    const initialSqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    await pool.initialize(initialSqrtPriceX96);

    // Fund deployer with tokens for minting and swap
    await usdc.mint(deployer.address, ethers.utils.parseUnits("200000", 6));
    await usdc.transfer(pool.address, ethers.utils.parseUnits("200000", 6));
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("1000", 18));

    // Approve callee for both tokens before mint
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Use a tick range that includes the current price (0) and a reasonable liquidity value
    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const liquidity = 1000;
    // Mint liquidity using callee
    await callee.mint(pool.address, deployer.address, tickLower, tickUpper, liquidity);

    // Check pool liquidity after mint
    const poolLiquidity = await pool.liquidity();
    expect(poolLiquidity).to.be.gt(0);

    // Approve callee for both tokens before swap
    await usdc.approve(callee.address, ethers.constants.MaxUint256);
    await lcoin.approve(callee.address, ethers.constants.MaxUint256);

    // Set required stablecoin liquidity to 200,000 USDC (6 decimals)
    await pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.utils.parseUnits('200000', 6));

    // Advance time to after ETP start
    const latestBlock = await ethers.provider.getBlock('latest');
    const nextTimestamp = Math.max(now + 100, latestBlock.timestamp + 10);
    await ethers.provider.send('evm_setNextBlockTimestamp', [nextTimestamp]);
    await ethers.provider.send('evm_mine', []);

    // Perform a swap: swap 1000 USDC for LCoin using callee
    const usdcBalanceBefore = await usdc.balanceOf(deployer.address);
    const lcoinBalanceBefore = await lcoin.balanceOf(deployer.address);
    const MIN_SQRT_RATIO = ethers.BigNumber.from('**********');
    await callee.swapExact0For1(pool.address, ethers.utils.parseUnits("1000", 6), deployer.address, MIN_SQRT_RATIO.add(1));
    const usdcBalanceAfter = await usdc.balanceOf(deployer.address);
    const lcoinBalanceAfter = await lcoin.balanceOf(deployer.address);
    expect(usdcBalanceAfter.lt(usdcBalanceBefore)).to.be.true;
    expect(lcoinBalanceAfter.gt(lcoinBalanceBefore)).to.be.true;

    // Burn liquidity (remove)
    await pool.burn(tickLower, tickUpper, liquidity);
    // Collect fees and withdrawn tokens
    const maxUint128 = ethers.BigNumber.from('0xffffffffffffffffffffffffffffffff');
    await pool.collect(deployer.address, tickLower, tickUpper, maxUint128, maxUint128);
    // After burn, liquidity should be zero
    const poolLiquidityAfter = await pool.liquidity();
    expect(poolLiquidityAfter).to.equal(0);
  });
}); 