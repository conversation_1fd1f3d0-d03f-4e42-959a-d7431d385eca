import { ethers, upgrades, network } from "hardhat";
import { expect } from "chai";
import { Contract } from "ethers";

describe.skip("USDC/LCoin Pool on Mainnet Fork via periphery", function () {
  let deployer: any;
  let usdc: Contract;
  let lcoin: Contract;
  let factory: Contract;
  let oracleRegistry: Contract;
  let poolLogic: Contract;
  let pool: Contract;
  let periphery: Contract;
  let swapRouter: Contract;
  let snapshotId: any;

  before(async () => {
    [deployer] = await ethers.getSigners();
    console.log("Deployer address:", deployer.address);
    console.log("Network:", network.name);

    // Deploy test USDC (6 decimals) and LCoin (18 decimals)
    const USDC = await ethers.getContractFactory("TestERC20");
    usdc = await USDC.deploy(ethers.utils.parseUnits("**********", 6), 6);
    await usdc.deployed();
    console.log("USDC deployed to:", usdc.address);

    const LCoin = await ethers.getContractFactory("TestERC20");
    lcoin = await LCoin.deploy(ethers.utils.parseUnits("**********", 18), 18);
    await lcoin.deployed();
    console.log("LCoin deployed to:", lcoin.address);
  });

  beforeEach(async () => {
    snapshotId = await ethers.provider.send("evm_snapshot", []);

    // Deploy UniswapV3Pool logic
    const UniswapV3Pool = await ethers.getContractFactory("UniswapV3Pool");
    poolLogic = await UniswapV3Pool.deploy();
    await poolLogic.deployed();

    // Deploy UniswapV3Factory (Upgradeable)
    const UniswapV3Factory = await ethers.getContractFactory("UniswapV3Factory");
    factory = await upgrades.deployProxy(
      UniswapV3Factory,
      [
        deployer.address,
        ethers.constants.AddressZero, // temp oracleRegistry
        poolLogic.address
      ],
      { initializer: "initialize", kind: "transparent" }
    );
    await factory.deployed();

    // Deploy OracleRegistry
    const OracleRegistry = await ethers.getContractFactory("OracleRegistry");
    oracleRegistry = await OracleRegistry.deploy(factory.address);
    await oracleRegistry.deployed();

    // Set OracleRegistry in factory
    await factory.setOracleRegistry(oracleRegistry.address);

    // Deploy and set mock Chainlink oracles for USDC and LCoin
    const MockChainlinkOracle = await ethers.getContractFactory("MockChainlinkOracle");
    const mockOracleUsdc = await MockChainlinkOracle.deploy(ethers.BigNumber.from("1000000")); // 1 USDC = 1 USD, 6 decimals
    await mockOracleUsdc.deployed();

    await factory.setOracle(usdc.address, mockOracleUsdc.address);
    console.log("Oracle set successfully");
    await factory.setVault("******************************************");
    console.log("Vault set successfully");
    // Debug: Check code at vault address
    const vaultCode = await ethers.provider.getCode("******************************************");
    console.log("Vault contract code:", vaultCode);
    if (vaultCode === "0x") {
      throw new Error("No contract code at the specified vault address! Check fork block number and address.");
    }

    
  
    const WETH9 = process.env.WETH9_ADDRESS || "******************************************"; // mainnet WETH9
    const TOKEN_DESCRIPTOR = process.env.TOKEN_DESCRIPTOR_ADDRESS || "******************************************";
    const PROJECT_MANAGER = deployer.address;
  
    const NonfungiblePositionManager = await ethers.getContractFactory("NonfungiblePositionManager");
    periphery = await NonfungiblePositionManager.deploy(
      factory.address,
      WETH9,
      TOKEN_DESCRIPTOR,
      PROJECT_MANAGER
    );
    await periphery.deployed();
  
    await factory.setPeriphery(periphery.address);

    // Deploy SwapRouter
    const SwapRouter = await ethers.getContractFactory("SwapRouter");
    swapRouter = await SwapRouter.deploy(factory.address, WETH9);
    await swapRouter.deployed();
  });

  afterEach(async () => {
    await ethers.provider.send("evm_revert", [snapshotId]);
  });

  it("deploys contracts, creates pool, funds with 200K USDC, and swaps", async () => {
    const fee = 3000;
  
    // Prepare launch params
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now + 60,
      exclusiveTradingPeriodEnd: now + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: swapRouter.address,
      projectManager: deployer.address
    };
    console.log("Launch params:", launchParams);

    // Always use USDC as token0
    const token0 = usdc.address;
    const token1 = lcoin.address;

    // Prepare create pool params
    const sqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    const params1 = {
      token0,
      token1,
      fee,
      sqrtPriceX96,
      launchParams
    };

    try {
      console.log("Calling createAndInitializePoolIfNecessaryWithParams on periphery...");
      console.log("Params:", params1);
      const tx = await periphery.createAndInitializePoolIfNecessaryWithParams(params1);
      console.log("Transaction sent:", tx.hash);
      const receipt = await tx.wait();
      console.log("Transaction confirmed in block:", receipt.blockNumber);
      
      // Get pool address from factory
      let poolAddress = await factory.getPool(token0, token1, fee);
      console.log("Pool created at:", poolAddress);
      
      // Verify pool exists
      expect(poolAddress).to.not.equal(ethers.constants.AddressZero);
      
      // Get pool contract
      pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);
      console.log("Pool contract initialized");

      await pool.setPeriphery(periphery.address);
      console.log("Periphery set in pool");

      // Verify SwapRouter is whitelisted
      const isWhitelisted = await pool.whitelistedAddressForSwap();
      console.log("Whitelisted address for swap:", isWhitelisted);
      expect(isWhitelisted.toLowerCase()).to.equal(swapRouter.address.toLowerCase());

      // Verify ETP is active
      let etpStart = await pool.exclusiveTradingPeriodStart();
      const etpEnd = await pool.exclusiveTradingPeriodEnd();
      const currentTime = Math.floor(Date.now() / 1000);
      console.log("Current time:", currentTime);
      console.log("ETP start:", etpStart.toString());
      console.log("ETP end:", etpEnd.toString());
      // expect(currentTime).to.be.gt(etpStart);
      // expect(currentTime).to.be.lt(etpEnd);

      // Wait for a few blocks to ensure pool is fully initialized
      await network.provider.send("evm_mine");
      await network.provider.send("evm_mine");
      
      // Fund deployer with tokens for minting and swap
      await usdc.mint(deployer.address, ethers.utils.parseUnits("300000", 6));
      await lcoin.mint(deployer.address, ethers.utils.parseUnits("300000", 18));

      // Approve periphery for both tokens before mint
      await usdc.approve(periphery.address, ethers.constants.MaxUint256);
      await lcoin.approve(periphery.address, ethers.constants.MaxUint256);

      // Use a tick range that includes the current price (0) and a large liquidity value
      const tickSpacing = await pool.tickSpacing();
      const tickLower = -tickSpacing;
      const tickUpper = tickSpacing;

      // Get current liquidity
      const liquidityBefore = await pool.liquidity();
      console.log("Liquidity before:", liquidityBefore.toString());

      const amount0Desired = ethers.utils.parseUnits('300000', 6); // 300,000 USDC
      const amount1Desired = ethers.utils.parseUnits('300000', 18); // 300,000 Project Token

      // Approve periphery for both tokens before mint (repeat to ensure approval)
      const usdcAllowance = await usdc.allowance(deployer.address, periphery.address);
      const lcoinAllowance = await lcoin.allowance(deployer.address, periphery.address);
      console.log("USDC allowance for periphery:", usdcAllowance.toString());
      console.log("LCoin allowance for periphery:", lcoinAllowance.toString());
      if (usdcAllowance.lt(amount0Desired)) {
        await usdc.approve(periphery.address, ethers.constants.MaxUint256);
        console.log("USDC approved for periphery");
      }
      if (lcoinAllowance.lt(amount1Desired)) {
        await lcoin.approve(periphery.address, ethers.constants.MaxUint256);
        console.log("LCoin approved for periphery");
      }

      // Define params for mint
      const params = {
        token0,
        token1,
        fee,
        tickLower,
        tickUpper,
        amount0Desired,
        amount1Desired,
        amount0Min: 0,
        amount1Min: 0,
        recipient: deployer.address,
        deadline: Math.floor(Date.now() / 1000) + 3600
      };
      console.log("Params:", {
        ...params,
        amount0Desired: ethers.utils.formatUnits(params.amount0Desired, 6),
        amount1Desired: ethers.utils.formatUnits(params.amount1Desired, 18)
      });

      // Add liquidity
      try {
        const tx = await periphery.mint(params);
        const receipt = await tx.wait();
        console.log("Liquidity added successfully! Transaction hash:", receipt.transactionHash);
      } catch (error) {
        console.error("Error adding liquidity:", error);
        // Print pool code at the address for debugging
        const code = await ethers.provider.getCode(poolAddress);
        console.error("Code at pool address:", code);
        throw error;
      }

      // Check final liquidity
      const liquidityAfter = await pool.liquidity();
      console.log("Liquidity after:", liquidityAfter.toString());
      
      // Prepare for swap
      console.log("Preparing for swap...");
      
      // Approve tokens for swap
      await usdc.approve(swapRouter.address, ethers.constants.MaxUint256);
      await lcoin.approve(swapRouter.address, ethers.constants.MaxUint256);
      
      // Get balances before swap
      const usdcBalanceBefore = await usdc.balanceOf(deployer.address);
      const lcoinBalanceBefore = await lcoin.balanceOf(deployer.address);
      console.log("USDC balance before:", ethers.utils.formatUnits(usdcBalanceBefore, 6));
      console.log("LCoin balance before:", ethers.utils.formatUnits(lcoinBalanceBefore, 18));

      // Set required stablecoin liquidity to 200,000 USDC (6 decimals)
      await pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.utils.parseUnits('200000', 6));

      // Verify required stablecoin liquidity is set
      const requiredLiquidity = await pool.requiredStableCoinLiquidityFairLaunch();
      console.log("Required stablecoin liquidity:", ethers.utils.formatUnits(requiredLiquidity, 6));
      expect(requiredLiquidity).to.equal(ethers.utils.parseUnits('200000', 6));

      // Verify pool has enough USDC
      const poolUsdcBalance = await usdc.balanceOf(pool.address);
      console.log("Pool USDC balance:", ethers.utils.formatUnits(poolUsdcBalance, 6));
      expect(poolUsdcBalance).to.be.gte(requiredLiquidity);

      // Verify SwapRouter contract code exists
      const swapRouterCode = await ethers.provider.getCode(swapRouter.address);
      console.log("SwapRouter code exists:", swapRouterCode !== "0x");
      expect(swapRouterCode).to.not.equal("0x");

      // Verify token approvals for SwapRouter
      const usdcAllowanceRouter = await usdc.allowance(deployer.address, swapRouter.address);
      const lcoinAllowanceRouter = await lcoin.allowance(deployer.address, swapRouter.address);
      console.log("USDC allowance for SwapRouter:", usdcAllowanceRouter.toString());
      console.log("LCoin allowance for SwapRouter:", lcoinAllowanceRouter.toString());

      // Re-approve if necessary
      if (usdcAllowanceRouter.lt(ethers.utils.parseUnits("100", 6))) {
        await usdc.approve(swapRouter.address, ethers.constants.MaxUint256);
        console.log("Re-approved USDC for SwapRouter");
      }
      if (lcoinAllowanceRouter.eq(0)) {
        await lcoin.approve(swapRouter.address, ethers.constants.MaxUint256);
        console.log("Re-approved LCoin for SwapRouter");
      }

      // Advance time to after ETP start
      etpStart = await pool.exclusiveTradingPeriodStart();
      const latestBlock = await ethers.provider.getBlock('latest');
      let nextTimestamp = etpStart.toNumber() + 10;
      if (nextTimestamp <= latestBlock.timestamp) {
        nextTimestamp = latestBlock.timestamp + 5;
      }
      await ethers.provider.send('evm_setNextBlockTimestamp', [nextTimestamp]);
      await ethers.provider.send('evm_mine', []);

      // Verify pool is unlocked
      const slot0 = await pool.slot0();
      console.log("Pool slot0:", slot0);
      expect(slot0.unlocked).to.be.true;

      // Prepare swap parameters
      const swapParams = {
        tokenIn: usdc.address,
        tokenOut: lcoin.address,
        fee: fee,
        recipient: deployer.address,
        deadline: Math.floor(Date.now() / 1000) + 3600,
        amountIn: ethers.utils.parseUnits("100", 6), // Swap 100 USDC
        amountOutMinimum: 0, // No slippage check for test
        sqrtPriceLimitX96: 0 // No price limit
      };

      console.log("Swap parameters:", {
        ...swapParams,
        amountIn: ethers.utils.formatUnits(swapParams.amountIn, 6)
      });

      // Execute swap
      console.log("Executing swap...");
      try {
        // Get the pool address directly from the factory
        const poolAddress = await factory.getPool(usdc.address, lcoin.address, fee);
        console.log("Using pool address:", poolAddress);

        // Use exactInputSingle instead of exactInput for simpler path handling
        const swapTx = await swapRouter.exactInputSingle({
          tokenIn: usdc.address,
          tokenOut: lcoin.address,
          fee: fee,
          recipient: deployer.address,
          deadline: Math.floor(Date.now() / 1000) + 3600,
          amountIn: ethers.utils.parseUnits("100", 6),
          amountOutMinimum: 0,
          sqrtPriceLimitX96: 0
        });
        console.log("Swap transaction sent:", swapTx.hash);
        const swapReceipt = await swapTx.wait();
        console.log("Swap executed in block:", swapReceipt.blockNumber);
      } catch (error) {
        console.error("Error executing swap:", error);
        // Get more details about the state
        const poolState = {
          liquidity: await pool.liquidity(),
          slot0: await pool.slot0(),
          isWhitelisted: await pool.whitelistedAddressForSwap(),
          etpActive: await pool.exclusiveTradingPeriodStart()
        };
        console.error("Pool state:", poolState);
        throw error;
      }

      // Get balances after swap
      const usdcBalanceAfter = await usdc.balanceOf(deployer.address);
      const lcoinBalanceAfter = await lcoin.balanceOf(deployer.address);
      console.log("USDC balance after:", ethers.utils.formatUnits(usdcBalanceAfter, 6));
      console.log("LCoin balance after:", ethers.utils.formatUnits(lcoinBalanceAfter, 18));

      // Verify swap was successful
      expect(usdcBalanceAfter.lt(usdcBalanceBefore)).to.be.true;
      expect(lcoinBalanceAfter.gt(lcoinBalanceBefore)).to.be.true;
      
    } catch (err) {
      console.error("Error creating pool via periphery:", err);
      throw err;
    }
  });

  it("adds liquidity via project manager (mintByProjectManager logic)", async () => {
    const fee = 3000;
    
    // Prepare launch params
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now + 600,
      exclusiveTradingPeriodEnd: now + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: swapRouter.address,
      projectManager: deployer.address
    };
    const token0 = usdc.address;
    const token1 = lcoin.address;
    const sqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    const params1 = {
      token0,
      token1,
      fee,
      sqrtPriceX96,
      launchParams
    };
    const tx = await periphery.createAndInitializePoolIfNecessaryWithParams(params1);
    await tx.wait();
    let poolAddress = await factory.getPool(token0, token1, fee);
    
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);
    await pool.setPeriphery(periphery.address);
    console.log("Periphery set in pool");

    // Fund deployer with tokens for minting and swap
    await usdc.mint(deployer.address, ethers.utils.parseUnits("300000", 6));
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("300000", 18));
    await usdc.approve(periphery.address, ethers.constants.MaxUint256);
    await lcoin.approve(periphery.address, ethers.constants.MaxUint256);

    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const amount0Desired = ethers.utils.parseUnits('300000', 6); // 300,000 USDC
    const amount1Desired = ethers.utils.parseUnits('300000', 18); // 300,000 Project Token

    // Define params for mint by project manager
    const params = {
      token0: usdc.address,
      token1: lcoin.address,
      fee,
      tickLower,
      tickUpper,
      amount0Desired,
      amount1Desired,
      amount0Min: 0,
      amount1Min: 0,
      recipient: deployer.address,
      deadline: Math.floor(Date.now() / 1000) + 3600
    };

    // Add liquidity as project manager
    const mintTx = await periphery.mint(params);
    const receipt = await mintTx.wait();
    expect(receipt.status).to.equal(1);
    // Check final liquidity
    const liquidityAfter = await pool.liquidity();
    expect(liquidityAfter).to.be.gt(0);

    // Prepare for swap
    console.log("Preparing for swap...");
    await usdc.approve(swapRouter.address, ethers.constants.MaxUint256);
    await lcoin.approve(swapRouter.address, ethers.constants.MaxUint256);

    // Get balances before swap
    const usdcBalanceBefore = await usdc.balanceOf(deployer.address);
    const lcoinBalanceBefore = await lcoin.balanceOf(deployer.address);
    console.log("USDC balance before:", ethers.utils.formatUnits(usdcBalanceBefore, 6));
    console.log("LCoin balance before:", ethers.utils.formatUnits(lcoinBalanceBefore, 18));

    // Set required stablecoin liquidity to 200,000 USDC (6 decimals)
    await pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.utils.parseUnits('200000', 6));
    const requiredLiquidity = await pool.requiredStableCoinLiquidityFairLaunch();
    console.log("Required stablecoin liquidity:", ethers.utils.formatUnits(requiredLiquidity, 6));
    expect(requiredLiquidity).to.equal(ethers.utils.parseUnits('200000', 6));

    // Verify pool has enough USDC
    const poolUsdcBalance = await usdc.balanceOf(pool.address);
    console.log("Pool USDC balance:", ethers.utils.formatUnits(poolUsdcBalance, 6));
    expect(poolUsdcBalance).to.be.gte(requiredLiquidity);

    // Verify SwapRouter contract code exists
    const swapRouterCode = await ethers.provider.getCode(swapRouter.address);
    console.log("SwapRouter code exists:", swapRouterCode !== "0x");
    expect(swapRouterCode).to.not.equal("0x");

    // Verify token approvals for SwapRouter
    const usdcAllowanceRouter = await usdc.allowance(deployer.address, swapRouter.address);
    const lcoinAllowanceRouter = await lcoin.allowance(deployer.address, swapRouter.address);
    console.log("USDC allowance for SwapRouter:", usdcAllowanceRouter.toString());
    console.log("LCoin allowance for SwapRouter:", lcoinAllowanceRouter.toString());

    // Re-approve if necessary
    if (usdcAllowanceRouter.lt(ethers.utils.parseUnits("100", 6))) {
      await usdc.approve(swapRouter.address, ethers.constants.MaxUint256);
      console.log("Re-approved USDC for SwapRouter");
    }
    if (lcoinAllowanceRouter.eq(0)) {
      await lcoin.approve(swapRouter.address, ethers.constants.MaxUint256);
      console.log("Re-approved LCoin for SwapRouter");
    }

    // Advance time to after ETP start
    let etpStart = await pool.exclusiveTradingPeriodStart();
    const latestBlock = await ethers.provider.getBlock('latest');
    let nextTimestamp = etpStart.toNumber() + 100;
    if (nextTimestamp <= latestBlock.timestamp) {
      nextTimestamp = latestBlock.timestamp + 50;
    }
    await ethers.provider.send('evm_setNextBlockTimestamp', [nextTimestamp]);
    await ethers.provider.send('evm_mine', []);

    // Verify pool is unlocked
    const slot0 = await pool.slot0();
    console.log("Pool slot0:", slot0);
    expect(slot0.unlocked).to.be.true;

    // Prepare swap parameters
    const swapParams = {
      tokenIn: usdc.address,
      tokenOut: lcoin.address,
      fee: fee,
      recipient: deployer.address,
      deadline: Math.floor(Date.now() / 1000) + 3600,
      amountIn: ethers.utils.parseUnits("100", 6), // Swap 100 USDC
      amountOutMinimum: 0, // No slippage check for test
      sqrtPriceLimitX96: 0 // No price limit
    };

    console.log("Swap parameters:", {
      ...swapParams,
      amountIn: ethers.utils.formatUnits(swapParams.amountIn, 6)
    });

    // Execute swap
    console.log("Executing swap...");
    try {
      const poolAddress = await factory.getPool(usdc.address, lcoin.address, fee);
      console.log("Using pool address:", poolAddress);
      const swapTx = await swapRouter.exactInputSingle({
        tokenIn: usdc.address,
        tokenOut: lcoin.address,
        fee: fee,
        recipient: deployer.address,
        deadline: Math.floor(Date.now() / 1000) + 3600,
        amountIn: ethers.utils.parseUnits("100", 6),
        amountOutMinimum: 0,
        sqrtPriceLimitX96: 0
      });
      console.log("Swap transaction sent:", swapTx.hash);
      const swapReceipt = await swapTx.wait();
      console.log("Swap executed in block:", swapReceipt.blockNumber);
    } catch (error) {
      console.error("Error executing swap:", error);
      const poolState = {
        liquidity: await pool.liquidity(),
        slot0: await pool.slot0(),
        isWhitelisted: await pool.whitelistedAddressForSwap(),
        etpActive: await pool.exclusiveTradingPeriodStart()
      };
      console.error("Pool state:", poolState);
      throw error;
    }

    // Get balances after swap
    const usdcBalanceAfter = await usdc.balanceOf(deployer.address);
    const lcoinBalanceAfter = await lcoin.balanceOf(deployer.address);
    console.log("USDC balance after:", ethers.utils.formatUnits(usdcBalanceAfter, 6));
    console.log("LCoin balance after:", ethers.utils.formatUnits(lcoinBalanceAfter, 18));

    // Verify swap was successful
    expect(usdcBalanceAfter.lt(usdcBalanceBefore)).to.be.true;
    expect(lcoinBalanceAfter.gt(lcoinBalanceBefore)).to.be.true;
  });

  it("adds and removes liquidity via periphery", async () => {
    const fee = 3000;
    
    // Prepare launch params
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now + 600,
      exclusiveTradingPeriodEnd: now + 3600,
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: swapRouter.address,
      projectManager: deployer.address
    };
    const token0 = usdc.address;
    const token1 = lcoin.address;
    const sqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    const params1 = {
      token0,
      token1,
      fee,
      sqrtPriceX96,
      launchParams
    };
    const tx = await periphery.createAndInitializePoolIfNecessaryWithParams(params1);
    await tx.wait();
    let poolAddress = await factory.getPool(token0, token1, fee);
    
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);
    await pool.setPeriphery(periphery.address);
    console.log("Periphery set in pool");

    // Fund deployer with tokens for minting and swap
    await usdc.mint(deployer.address, ethers.utils.parseUnits("300000", 6));
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("300000", 18));
    await usdc.approve(periphery.address, ethers.constants.MaxUint256);
    await lcoin.approve(periphery.address, ethers.constants.MaxUint256);

    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const amount0Desired = ethers.utils.parseUnits('300000', 6); // 300,000 USDC
    const amount1Desired = ethers.utils.parseUnits('300000', 18); // 300,000 Project Token

    // Define params for mint
    const mintParams = {
      token0: usdc.address,
      token1: lcoin.address,
      fee,
      tickLower,
      tickUpper,
      amount0Desired,
      amount1Desired,
      amount0Min: 0,
      amount1Min: 0,
      recipient: deployer.address,
      deadline: Math.floor(Date.now() / 1000) + 3600
    };

    // Add liquidity
    console.log("Adding liquidity...");
    const mintTx = await periphery.mint(mintParams);
    const mintReceipt = await mintTx.wait();
    expect(mintReceipt.status).to.equal(1);

    // Get token ID of the minted position
    const mintEvent = mintReceipt.events?.find((event: { event: string }) => event.event === "IncreaseLiquidity");
    expect(mintEvent).to.not.be.undefined;
    const tokenId = mintEvent.args.tokenId;
    console.log("Position token ID:", tokenId.toString());

    // Get position details
    const position = await periphery.positions(tokenId);
    console.log("Position liquidity:", position.liquidity.toString());
    expect(position.liquidity).to.be.gt(0);

    // Wait for some time
    await ethers.provider.send("evm_increaseTime", [7500]); // Advance 1 hour
    await ethers.provider.send("evm_mine", []);

    // Remove liquidity
    console.log("Removing liquidity...");
    const decreaseLiquidityParams = {
      tokenId,
      liquidity: position.liquidity,
      amount0Min: 0,
      amount1Min: 0,
      deadline: Math.floor(Date.now() / 1000) + 7600 // Increase deadline to 2 hours from now
    };

    const removeTx = await periphery.decreaseLiquidity(decreaseLiquidityParams);
    const removeReceipt = await removeTx.wait();
    expect(removeReceipt.status).to.equal(1);

    // Collect fees
    const collectParams = {
      tokenId,
      recipient: deployer.address,
      amount0Max: ethers.utils.parseUnits('300000', 6), // Max 300,000 USDC
      amount1Max: ethers.utils.parseUnits('300000', 18) // Max 300,000 LCoin
    };

    const collectTx = await periphery.collect(collectParams);
    const collectReceipt = await collectTx.wait();
    expect(collectReceipt.status).to.equal(1);

    // Verify position is empty
    const positionAfter = await periphery.positions(tokenId);
    expect(positionAfter.liquidity).to.equal(0);
  });

  it("project manager liquidity removal restrictions after ETP", async () => {
    const fee = 3000;
    const now = Math.floor(Date.now() / 1000);
    const launchParams = {
      tokenLaunchType: 0, // FairLaunch
      exclusiveTradingPeriodStart: now + 60, // ETP starts in 1 minute
      exclusiveTradingPeriodEnd: now + 3600, // ETP ends in 1 hour
      extendedLiquidityLockDuration: 3600,
      whitelistedAddressForSwap: swapRouter.address,
      projectManager: deployer.address
    };

    // Create and initialize pool
    const token0 = usdc.address;
    const token1 = lcoin.address;
    const sqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
    const params1 = {
      token0,
      token1,
      fee,
      sqrtPriceX96,
      launchParams
    };
    await periphery.createAndInitializePoolIfNecessaryWithParams(params1);
    const poolAddress = await factory.getPool(token0, token1, fee);
    pool = await ethers.getContractAt("UniswapV3Pool", poolAddress);
    await pool.setPeriphery(periphery.address);

    // Fund deployer with tokens for minting
    await usdc.mint(deployer.address, ethers.utils.parseUnits("250000", 6)); // 250K USDC
    await lcoin.mint(deployer.address, ethers.utils.parseUnits("250000", 18)); // 250K LCoin
    await usdc.approve(periphery.address, ethers.constants.MaxUint256);
    await lcoin.approve(periphery.address, ethers.constants.MaxUint256);

    const tickSpacing = await pool.tickSpacing();
    const tickLower = -tickSpacing;
    const tickUpper = tickSpacing;
    const amount0Desired = ethers.utils.parseUnits('250000', 6); // 250K USDC
    const amount1Desired = ethers.utils.parseUnits('250000', 18); // 250K LCoin

    // Add initial liquidity as project manager
    const mintParams = {
      token0: usdc.address,
      token1: lcoin.address,
      fee,
      tickLower,
      tickUpper,
      amount0Desired,
      amount1Desired,
      amount0Min: 0,
      amount1Min: 0,
      recipient: deployer.address,
      deadline: Math.floor(Date.now() / 1000) + 3600
    };

    const mintTx = await periphery.mint(mintParams);
    const mintReceipt = await mintTx.wait();
    const mintEvent = mintReceipt.events?.find((event: { event: string }) => event.event === "IncreaseLiquidity");
    const tokenId = mintEvent.args.tokenId;
    console.log("Position token ID:", tokenId.toString());

    // Get initial position details
    const initialPosition = await periphery.positions(tokenId);
    console.log("Initial position liquidity:", initialPosition.liquidity.toString());
    expect(initialPosition.liquidity).to.be.gt(0);

    // Set required stablecoin liquidity to 200,000 USDC
    await pool.updateRequiredStableCoinLiquidityFairLaunch(ethers.utils.parseUnits('200000', 6));

    // Advance time to ETP start + 30 seconds
    await ethers.provider.send("evm_setNextBlockTimestamp", [now + 90]);
    await ethers.provider.send("evm_mine", []);

    // Try to remove all liquidity during ETP (should fail)
    const decreaseAllParams = {
      tokenId,
      liquidity: initialPosition.liquidity,
      amount0Min: 0,
      amount1Min: 0,
      deadline: Math.floor(Date.now() / 1000) + 36000
    };
    await expect(periphery.decreaseLiquidity(decreaseAllParams)).to.be.reverted;

    await ethers.provider.send("evm_setNextBlockTimestamp", [now + 7200]);
    await ethers.provider.send("evm_mine", []);

    // Calculate excess liquidity (50K worth)
    const excessLiquidity = initialPosition.liquidity.div(5); // 20% of total liquidity = 50K
    
    // Remove excess liquidity during ETP (should succeed)
    const decreaseExcessParams = {
      tokenId,
      liquidity: excessLiquidity,
      amount0Min: 0,
      amount1Min: 0,
      deadline: Math.floor(Date.now() / 1000) + 36000
    };
    const removeTx = await periphery.decreaseLiquidity(decreaseExcessParams);
    const removeReceipt = await removeTx.wait();
    expect(removeReceipt.status).to.equal(1);

    // Collect the removed liquidity
    const collectParams = {
      tokenId,
      recipient: deployer.address,
      amount0Max: ethers.utils.parseUnits('50000', 6), // 50K USDC
      amount1Max: ethers.utils.parseUnits('50000', 18) // 50K LCoin
    };
    await periphery.collect(collectParams);

    // Verify remaining position
    const midPosition = await periphery.positions(tokenId);
    expect(midPosition.liquidity).to.be.lt(initialPosition.liquidity);
    
    // Verify pool still has required liquidity
    const poolUsdcBalance = await usdc.balanceOf(pool.address);
    expect(poolUsdcBalance).to.be.gte(ethers.utils.parseUnits('200000', 6));

    // Remove remaining liquidity after ETP (should succeed)
    const decreaseRemainingParams = {
      tokenId,
      liquidity: midPosition.liquidity,
      amount0Min: 0,
      amount1Min: 0,
      deadline: Math.floor(Date.now() / 1000) + 36000
    };
    const removeFinalTx = await periphery.decreaseLiquidity(decreaseRemainingParams);
    const removeFinalReceipt = await removeFinalTx.wait();
    expect(removeFinalReceipt.status).to.equal(1);

    // Collect the final liquidity
    const collectFinalParams = {
      tokenId,
      recipient: deployer.address,
      amount0Max: ethers.utils.parseUnits('200000', 6), // Remaining 200K USDC
      amount1Max: ethers.utils.parseUnits('200000', 18) // Remaining 200K LCoin
    };
    await periphery.collect(collectFinalParams);

    // Verify position is empty
    const finalPosition = await periphery.positions(tokenId);
    expect(finalPosition.liquidity).to.equal(0);
  });
}); 