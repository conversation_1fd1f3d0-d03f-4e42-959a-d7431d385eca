// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SwapMath #computeSwapStep gas swap one for zero exact in capped 1`] = `2121`;

exports[`SwapMath #computeSwapStep gas swap one for zero exact in partial 1`] = `2820`;

exports[`SwapMath #computeSwapStep gas swap one for zero exact out capped 1`] = `1873`;

exports[`SwapMath #computeSwapStep gas swap one for zero exact out partial 1`] = `2820`;

exports[`SwapMath #computeSwapStep gas swap zero for one exact in capped 1`] = `2122`;

exports[`SwapMath #computeSwapStep gas swap zero for one exact in partial 1`] = `3160`;

exports[`SwapMath #computeSwapStep gas swap zero for one exact out capped 1`] = `1874`;

exports[`SwapMath #computeSwapStep gas swap zero for one exact out partial 1`] = `3160`;
