// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UniswapV3Pool gas tests fee is off #burn above current price burn entire position after some time passes 1`] = `97844`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price burn when only position using ticks 1`] = `97844`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price entire position burn but other positions are using the ticks 1`] = `95570`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price partial position burn 1`] = `100370`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price burn entire position after some time passes 1`] = `115139`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price burn when only position using ticks 1`] = `110012`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price entire position burn but other positions are using the ticks 1`] = `100381`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price partial position burn 1`] = `105181`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price burn entire position after some time passes 1`] = `107302`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price burn when only position using ticks 1`] = `107302`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price entire position burn but other positions are using the ticks 1`] = `96193`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price partial position burn 1`] = `100993`;

exports[`UniswapV3Pool gas tests fee is off #collect close to worst case 1`] = `48615`;

exports[`UniswapV3Pool gas tests fee is off #increaseObservationCardinalityNext grow by 1 slot 1`] = `51049`;

exports[`UniswapV3Pool gas tests fee is off #increaseObservationCardinalityNext no op 1`] = `24598`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price add to position after some time passes 1`] = `114942`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price add to position existing 1`] = `114942`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price new position mint first in range 1`] = `235908`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price second position in same range 1`] = `132042`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price add to position after some time passes 1`] = `156003`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price add to position existing 1`] = `146794`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price new position mint first in range 1`] = `337452`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price second position in same range 1`] = `163894`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price add to position after some time passes 1`] = `115474`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price add to position existing 1`] = `115474`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price new position mint first in range 1`] = `315606`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price second position in same range 1`] = `132574`;

exports[`UniswapV3Pool gas tests fee is off #poke best case 1`] = `52292`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick above 1`] = `29756`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick below 1`] = `29718`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick inside 1`] = `37178`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block moves tick, no initialized crossings 1`] = `120840`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block with no tick movement 1`] = `104684`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap crossing a single initialized tick 1`] = `146373`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap crossing several initialized ticks 1`] = `200732`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap, no initialized crossings 1`] = `135995`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 large swap crossing several initialized ticks after some time passes 1`] = `200732`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 large swap crossing several initialized ticks second time after some time passes 1`] = `219932`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block moves tick, no initialized crossings 1`] = `120840`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block with no tick movement 1`] = `104795`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block, large swap crossing a single initialized tick 1`] = `132654`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block, large swap crossing several initialized ticks 1`] = `186991`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price burn entire position after some time passes 1`] = `97844`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price burn when only position using ticks 1`] = `97844`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price entire position burn but other positions are using the ticks 1`] = `95570`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price partial position burn 1`] = `100370`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price burn entire position after some time passes 1`] = `115103`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price burn when only position using ticks 1`] = `109976`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price entire position burn but other positions are using the ticks 1`] = `100336`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price partial position burn 1`] = `105136`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price burn entire position after some time passes 1`] = `107302`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price burn when only position using ticks 1`] = `107266`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price entire position burn but other positions are using the ticks 1`] = `96148`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price partial position burn 1`] = `100948`;

exports[`UniswapV3Pool gas tests fee is on #collect close to worst case 1`] = `48615`;

exports[`UniswapV3Pool gas tests fee is on #increaseObservationCardinalityNext grow by 1 slot 1`] = `51049`;

exports[`UniswapV3Pool gas tests fee is on #increaseObservationCardinalityNext no op 1`] = `24598`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price add to position after some time passes 1`] = `117013`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price add to position existing 1`] = `117013`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price new position mint first in range 1`] = `237979`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price second position in same range 1`] = `134113`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price add to position after some time passes 1`] = `158118`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price add to position existing 1`] = `148909`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price new position mint first in range 1`] = `339567`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price second position in same range 1`] = `166009`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price add to position after some time passes 1`] = `117611`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price add to position existing 1`] = `117611`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price new position mint first in range 1`] = `317743`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price second position in same range 1`] = `134711`;

exports[`UniswapV3Pool gas tests fee is on #poke best case 1`] = `52292`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick above 1`] = `29756`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick below 1`] = `29718`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick inside 1`] = `37178`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block moves tick, no initialized crossings 1`] = `126227`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block with no tick movement 1`] = `109924`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap crossing a single initialized tick 1`] = `151907`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap crossing several initialized ticks 1`] = `206707`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap, no initialized crossings 1`] = `141676`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 large swap crossing several initialized ticks after some time passes 1`] = `206707`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 large swap crossing several initialized ticks second time after some time passes 1`] = `225907`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block moves tick, no initialized crossings 1`] = `126227`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block with no tick movement 1`] = `110035`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block, large swap crossing a single initialized tick 1`] = `138041`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block, large swap crossing several initialized ticks 1`] = `192775`;
