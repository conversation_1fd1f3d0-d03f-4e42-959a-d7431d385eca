// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BitMath #leastSignificantBit gas cost of max uint128 1`] = `443`;

exports[`BitMath #leastSignificantBit gas cost of max uint256 1`] = `443`;

exports[`BitMath #leastSignificantBit gas cost of smaller number 1`] = `441`;

exports[`BitMath #mostSignificantBit gas cost of max uint128 1`] = `385`;

exports[`BitMath #mostSignificantBit gas cost of max uint256 1`] = `403`;

exports[`BitMath #mostSignificantBit gas cost of smaller number 1`] = `313`;
