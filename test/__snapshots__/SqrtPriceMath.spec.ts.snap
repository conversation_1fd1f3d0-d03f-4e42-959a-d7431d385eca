// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SqrtPriceMath #getAmount0Delta gas cost for amount0 where roundUp = true 1`] = `628`;

exports[`SqrtPriceMath #getAmount0Delta gas cost for amount0 where roundUp = true 2`] = `496`;

exports[`SqrtPriceMath #getAmount1Delta gas cost for amount0 where roundUp = false 1`] = `496`;

exports[`SqrtPriceMath #getAmount1Delta gas cost for amount0 where roundUp = true 1`] = `628`;

exports[`SqrtPriceMath #getNextSqrtPriceFromInput zeroForOne = false gas 1`] = `536`;

exports[`SqrtPriceMath #getNextSqrtPriceFromInput zeroForOne = true gas 1`] = `771`;

exports[`SqrtPriceMath #getNextSqrtPriceFromOutput zeroForOne = false gas 1`] = `866`;

exports[`SqrtPriceMath #getNextSqrtPriceFromOutput zeroForOne = true gas 1`] = `441`;
