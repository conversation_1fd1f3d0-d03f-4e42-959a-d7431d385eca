import Decimal from 'decimal.js'
import { BigN<PERSON><PERSON>, BigNumberish, Wallet } from 'ethers'
import { ethers, waffle } from 'hardhat'
import { MockTimeUniswapV3Pool } from '../typechain/MockTimeUniswapV3Pool'
import { TickMathTest } from '../typechain/TickMathTest'
import { TestUniswapV3Callee } from '../typechain/TestUniswapV3Callee'
import { UniswapV3PoolSwapTest } from '../typechain/UniswapV3PoolSwapTest'
import { TestUniswapV3Router } from '../typechain/TestUniswapV3Router'
import { expect } from './shared/expect'

import { poolFixture } from './shared/fixtures'
import { formatPrice, formatTokenAmount } from './shared/format'

import {
  createPoolFunctions,
  encodePriceSqrt,
  expandTo18Decimals,
  FeeAmount,
  getMaxLiquidityPerTick,
  getMaxTick,
  getMinTick,
  MAX_SQRT_RATIO,
  MaxUint128,
  MIN_SQRT_RATIO,
  MintFunction,
  SwapFunction,
  TICK_SPACINGS,
} from './shared/utilities'

import { ethers } from 'hardhat'
import { constants } from 'ethers'
import { createFixtureLoader } from './shared/fixtures'
import { TestERC20 } from '../typechain/TestERC20'
import { UniswapV3Pool } from '../typechain/UniswapV3Pool'
import { getWalletsFixture, tokensFixture, factoryFixture } from './shared/fixtures'
import { SignerWithAddress } from '@nomiclabs/hardhat-ethers/signers'
import { loadFixture } from '@nomicfoundation/hardhat-network-helpers'

const {
  constants: { MaxUint256 },
} = ethers

const createFixtureLoader = waffle.createFixtureLoader

Decimal.config({ toExpNeg: -500, toExpPos: 500 })

function applySqrtRatioBipsHundredthsDelta(sqrtRatio: BigNumber, bipsHundredths: number): BigNumber {
  return BigNumber.from(
    new Decimal(
      sqrtRatio
        .mul(sqrtRatio)
        .mul(1e6 + bipsHundredths)
        .div(1e6)
        .toString()
    )
      .sqrt()
      .floor()
      .toString()
  )
}

describe.skip('UniswapV3Pool arbitrage tests', () => {
  let token0: TestERC20
  let token1: TestERC20
  let pool: UniswapV3Pool
  let callee: TestUniswapV3Callee
  let wallet: SignerWithAddress
  let other: SignerWithAddress

  beforeEach('deploy test contracts', async () => {
    const wallets = await ethers.getSigners()
    wallet = wallets[0]
    other = wallets[1]

    const { token0: _token0, token1: _token1, swapTargetCallee: _callee, createPool } = await poolFixture([wallet, other])
    token0 = _token0
    token1 = _token1
    callee = _callee

    pool = await createPool(3000, 60, token0, token1, wallet)

    // Mint liquidity before swaps
    const tickLower = -60
    const tickUpper = 60
    const amount = expandTo18Decimals(10)
    await token0.mint(wallet.address, amount)
    await token1.mint(wallet.address, amount)
    await token0.approve(pool.address, amount)
    await token1.approve(pool.address, amount)
    await pool.mint(wallet.address, tickLower, tickUpper, amount, '0x')

    await token0.approve(callee.address, constants.MaxUint256)
    await token1.approve(callee.address, constants.MaxUint256)
  })

  it('swapExact0For1', async () => {
    const tx = await callee.swapExact0For1(
      pool.address,
      expandTo18Decimals(1),
      wallet.address,
      MIN_SQRT_RATIO.add(1)
    )
    const receipt = await tx.wait()
    const event = receipt.events?.find(e => e.event === 'SwapCallback')
    expect(event?.args?.amount0Delta).to.eq(expandTo18Decimals(1))
    expect(event?.args?.amount1Delta).to.eq('-997000000000000000')
  })

  it('swap0ForExact1', async () => {
    const tx = await callee.swap0ForExact1(
      pool.address,
      expandTo18Decimals(1),
      wallet.address,
      MIN_SQRT_RATIO.add(1)
    )
    const receipt = await tx.wait()
    const event = receipt.events?.find(e => e.event === 'SwapCallback')
    expect(event?.args?.amount0Delta).to.eq('1003009027081243732')
    expect(event?.args?.amount1Delta).to.eq(expandTo18Decimals(1).mul(-1))
  })

  it('swapExact1For0', async () => {
    const tx = await callee.swapExact1For0(
      pool.address,
      expandTo18Decimals(1),
      wallet.address,
      MAX_SQRT_RATIO.sub(1)
    )
    const receipt = await tx.wait()
    const event = receipt.events?.find(e => e.event === 'SwapCallback')
    expect(event?.args?.amount0Delta).to.eq('-997000000000000000')
    expect(event?.args?.amount1Delta).to.eq(expandTo18Decimals(1))
  })

  it('swap1ForExact0', async () => {
    const tx = await callee.swap1ForExact0(
      pool.address,
      expandTo18Decimals(1),
      wallet.address,
      MAX_SQRT_RATIO.sub(1)
    )
    const receipt = await tx.wait()
    const event = receipt.events?.find(e => e.event === 'SwapCallback')
    expect(event?.args?.amount0Delta).to.eq(expandTo18Decimals(1).mul(-1))
    expect(event?.args?.amount1Delta).to.eq('1003009027081243732')
  })

  for (const feeProtocol of [0, 6]) {
    describe(`protocol fee = ${feeProtocol};`, () => {
      const startingPrice = encodePriceSqrt(1, 1)
      const startingTick = 0
      const feeAmount = FeeAmount.MEDIUM
      const tickSpacing = TICK_SPACINGS[feeAmount]
      const minTick = getMinTick(tickSpacing)
      const maxTick = getMaxTick(tickSpacing)

      for (const passiveLiquidity of [
        expandTo18Decimals(1).div(100),
        expandTo18Decimals(1),
        expandTo18Decimals(10),
        expandTo18Decimals(100),
      ]) {
        describe(`passive liquidity of ${formatTokenAmount(passiveLiquidity)}`, () => {
          const arbTestFixture = async ([wallet, arbitrageur]: Wallet[]) => {
            const fix = await poolFixture([wallet], waffle.provider)

            // Deploy SwapRouter
            const routerContractFactory = await ethers.getContractFactory('TestUniswapV3Router')
            const swapRouter = (await routerContractFactory.deploy()) as TestUniswapV3Router
            await swapRouter.deployed()

            // Approve tokens for router
            await fix.token0.approve(swapRouter.address, MaxUint256)
            await fix.token1.approve(swapRouter.address, MaxUint256)

            // Create pool with router as whitelisted address
            const pool = await fix.createPool(
              feeAmount,
              tickSpacing,
              undefined,
              undefined,
              undefined,
              {
                tokenLaunchType: 0, // FairLaunch
                exclusiveTradingPeriodStart: 0,
                exclusiveTradingPeriodEnd: 0,
                extendedLiquidityLockDuration: 0,
                projectManager: arbitrageur.address,
                whitelistedAddressForSwap: swapRouter.address // Use router for all swaps
              }
            )

            await fix.token0.transfer(arbitrageur.address, BigNumber.from(2).pow(254))
            await fix.token1.transfer(arbitrageur.address, BigNumber.from(2).pow(254))

            const {
              swapExact0For1,
              swapToHigherPrice,
              swapToLowerPrice,
              swapExact1For0,
              mint,
            } = await createPoolFunctions({
              swapTarget: swapRouter,
              token0: fix.token0,
              token1: fix.token1,
              pool,
            })

            const tickMathFactory = await ethers.getContractFactory('TickMathTest')
            const tickMath = (await tickMathFactory.deploy()) as TickMathTest

            await fix.token0.approve(swapRouter.address, MaxUint256)
            await fix.token1.approve(swapRouter.address, MaxUint256)

            await pool.initialize(startingPrice)
            if (feeProtocol != 0) await pool.setFeeProtocol(feeProtocol, feeProtocol)
            await mint(wallet.address, minTick, maxTick, passiveLiquidity)

            expect((await pool.slot0()).tick).to.eq(startingTick)
            expect((await pool.slot0()).sqrtPriceX96).to.eq(startingPrice)

            return { pool, swapExact0For1, mint, swapToHigherPrice, swapToLowerPrice, swapExact1For0, swapRouter, tickMath }
          }

          let swapExact0For1: SwapFunction
          let swapToHigherPrice: SwapFunction
          let swapToLowerPrice: SwapFunction
          let swapExact1For0: SwapFunction
          let pool: MockTimeUniswapV3Pool
          let mint: MintFunction
          let swapRouter: TestUniswapV3Router
          let tickMath: TickMathTest

          beforeEach('load the fixture', async () => {
            ;({
              swapExact0For1,
              pool,
              mint,
              swapToHigherPrice,
              swapToLowerPrice,
              swapExact1For0,
              swapRouter,
              tickMath
            } = await loadFixture(arbTestFixture))
          })

          async function simulateSwap(
            zeroForOne: boolean,
            amountSpecified: BigNumberish,
            sqrtPriceLimitX96?: BigNumber
          ): Promise<{
            executionPrice: BigNumber
            nextSqrtRatio: BigNumber
            amount0Delta: BigNumber
            amount1Delta: BigNumber
          }> {
            // Use exactInputSingle for simulation
            const params = {
              tokenIn: zeroForOne ? fix.token0.address : fix.token1.address,
              tokenOut: zeroForOne ? fix.token1.address : fix.token0.address,
              fee: feeAmount,
              recipient: wallet.address,
              deadline: Math.floor(Date.now() / 1000) + 3600,
              amountIn: amountSpecified,
              amountOutMinimum: 0,
              sqrtPriceLimitX96: sqrtPriceLimitX96 ?? (zeroForOne ? MIN_SQRT_RATIO.add(1) : MAX_SQRT_RATIO.sub(1))
            }

            const { amount0Delta, amount1Delta } = await swapRouter.callStatic.exactInputSingle(params)
            const nextSqrtRatio = sqrtPriceLimitX96 ?? (zeroForOne ? MIN_SQRT_RATIO.add(1) : MAX_SQRT_RATIO.sub(1))

            const executionPrice = zeroForOne
              ? encodePriceSqrt(amount1Delta, amount0Delta.mul(-1))
              : encodePriceSqrt(amount1Delta.mul(-1), amount0Delta)

            return { executionPrice, nextSqrtRatio, amount0Delta, amount1Delta }
          }

          for (const { zeroForOne, assumedTruePriceAfterSwap, inputAmount, description } of [
            {
              description: 'exact input of 10e18 token0 with starting price of 1.0 and true price of 0.98',
              zeroForOne: true,
              inputAmount: expandTo18Decimals(10),
              assumedTruePriceAfterSwap: encodePriceSqrt(98, 100),
            },
            {
              description: 'exact input of 10e18 token0 with starting price of 1.0 and true price of 1.01',
              zeroForOne: true,
              inputAmount: expandTo18Decimals(10),
              assumedTruePriceAfterSwap: encodePriceSqrt(101, 100),
            },
          ]) {
            describe(description, () => {
              function valueToken1(arbBalance0: BigNumber, arbBalance1: BigNumber) {
                return assumedTruePriceAfterSwap
                  .mul(assumedTruePriceAfterSwap)
                  .mul(arbBalance0)
                  .div(BigNumber.from(2).pow(192))
                  .add(arbBalance1)
              }

              it('not sandwiched', async () => {
                const { executionPrice, amount1Delta, amount0Delta } = await simulateSwap(zeroForOne, inputAmount)
                zeroForOne
                  ? await swapExact0For1(inputAmount, wallet.address)
                  : await swapExact1For0(inputAmount, wallet.address)

                expect({
                  executionPrice: formatPrice(executionPrice),
                  amount0Delta: formatTokenAmount(amount0Delta),
                  amount1Delta: formatTokenAmount(amount1Delta),
                  priceAfter: formatPrice((await pool.slot0()).sqrtPriceX96),
                }).to.matchSnapshot()
              })

              it('sandwiched with swap to execution price then mint max liquidity/target/burn max liquidity', async () => {
                const { executionPrice } = await simulateSwap(zeroForOne, inputAmount)

                const firstTickAboveMarginalPrice = zeroForOne
                  ? Math.ceil(
                      (await tickMath.getTickAtSqrtRatio(
                        applySqrtRatioBipsHundredthsDelta(executionPrice, feeAmount)
                      )) / tickSpacing
                    ) * tickSpacing
                  : Math.floor(
                      (await tickMath.getTickAtSqrtRatio(
                        applySqrtRatioBipsHundredthsDelta(executionPrice, -feeAmount)
                      )) / tickSpacing
                    ) * tickSpacing
                const tickAfterFirstTickAboveMarginPrice = zeroForOne
                  ? firstTickAboveMarginalPrice - tickSpacing
                  : firstTickAboveMarginalPrice + tickSpacing

                const priceSwapStart = await tickMath.getSqrtRatioAtTick(firstTickAboveMarginalPrice)

                let arbBalance0 = BigNumber.from(0)
                let arbBalance1 = BigNumber.from(0)

                // Frontrun simulation
                const {
                  amount0Delta: frontrunDelta0,
                  amount1Delta: frontrunDelta1,
                  executionPrice: frontrunExecutionPrice,
                } = await simulateSwap(zeroForOne, MaxUint256.div(2), priceSwapStart)
                arbBalance0 = arbBalance0.sub(frontrunDelta0)
                arbBalance1 = arbBalance1.sub(frontrunDelta1)

                // Actual frontrun swap
                zeroForOne
                  ? await swapToLowerPrice(priceSwapStart, arbitrageur.address)
                  : await swapToHigherPrice(priceSwapStart, arbitrageur.address)

                const profitToken1AfterFrontRun = valueToken1(arbBalance0, arbBalance1)

                const tickLower = zeroForOne ? tickAfterFirstTickAboveMarginPrice : firstTickAboveMarginalPrice
                const tickUpper = zeroForOne ? firstTickAboveMarginalPrice : tickAfterFirstTickAboveMarginPrice

                // deposit max liquidity at the tick
                const mintReceipt = await (
                  await mint(wallet.address, tickLower, tickUpper, getMaxLiquidityPerTick(tickSpacing))
                ).wait()
                // sub the mint costs
                const { amount0: amount0Mint, amount1: amount1Mint } = pool.interface.decodeEventLog(
                  pool.interface.events['Mint(address,address,int24,int24,uint128,uint256,uint256)'],
                  mintReceipt.events?.[2].data!
                )
                arbBalance0 = arbBalance0.sub(amount0Mint)
                arbBalance1 = arbBalance1.sub(amount1Mint)

                // execute the user's swap
                const { executionPrice: executionPriceAfterFrontrun } = await simulateSwap(zeroForOne, inputAmount)
                zeroForOne
                  ? await swapExact0For1(inputAmount, wallet.address)
                  : await swapExact1For0(inputAmount, wallet.address)

                // burn the arb's liquidity
                const { amount0: amount0Burn, amount1: amount1Burn } = await pool.callStatic.burn(
                  tickLower,
                  tickUpper,
                  getMaxLiquidityPerTick(tickSpacing)
                )
                await pool.burn(tickLower, tickUpper, getMaxLiquidityPerTick(tickSpacing))
                arbBalance0 = arbBalance0.add(amount0Burn)
                arbBalance1 = arbBalance1.add(amount1Burn)

                // add the fees as well
                const {
                  amount0: amount0CollectAndBurn,
                  amount1: amount1CollectAndBurn,
                } = await pool.callStatic.collect(arbitrageur.address, tickLower, tickUpper, MaxUint128, MaxUint128)
                const [amount0Collect, amount1Collect] = [
                  amount0CollectAndBurn.sub(amount0Burn),
                  amount1CollectAndBurn.sub(amount1Burn),
                ]
                arbBalance0 = arbBalance0.add(amount0Collect)
                arbBalance1 = arbBalance1.add(amount1Collect)

                const profitToken1AfterSandwich = valueToken1(arbBalance0, arbBalance1)

                // backrun the swap to true price, i.e. swap to the marginal price = true price
                const priceToSwapTo = zeroForOne
                  ? applySqrtRatioBipsHundredthsDelta(assumedTruePriceAfterSwap, -feeAmount)
                  : applySqrtRatioBipsHundredthsDelta(assumedTruePriceAfterSwap, feeAmount)
                const {
                  amount0Delta: backrunDelta0,
                  amount1Delta: backrunDelta1,
                  executionPrice: backrunExecutionPrice,
                } = await simulateSwap(!zeroForOne, MaxUint256.div(2), priceToSwapTo)
                
                if (zeroForOne) { // Original user swap was 0->1 (price up), backrun is 1->0 (arb sells token1)
                  await swapToHigherPrice(priceToSwapTo, arbitrageur.address)
                } else { // Original user swap was 1->0 (price down), backrun is 0->1 (arb sells token0)
                  await swapToLowerPrice(priceToSwapTo, arbitrageur.address)
                }
                arbBalance0 = arbBalance0.sub(backrunDelta0)
                arbBalance1 = arbBalance1.sub(backrunDelta1)

                const profitToken1AfterBackrun = valueToken1(arbBalance0, arbBalance1)

                expect(profitToken1AfterBackrun.gt(profitToken1AfterSandwich)).to.be.true
                expect(profitToken1AfterSandwich.gt(profitToken1AfterFrontRun)).to.be.true
              })

              it('backrun to true price after swap only', async () => {
                let arbBalance0 = BigNumber.from(0)
                let arbBalance1 = BigNumber.from(0)

                // User's actual swap (uses swapTarget)
                // Pool's whitelisted is tester.address initially. Update for swapTarget.
                await pool.updateWhitelistedAddressForSwap(swapRouter.address)
                zeroForOne
                  ? await swapExact0For1(inputAmount, wallet.address)
                  : await swapExact1For0(inputAmount, wallet.address)

                // Backrun simulation (uses tester)
                await pool.updateWhitelistedAddressForSwap(swapRouter.address)
                // swap to the marginal price = true price
                const priceToSwapTo = zeroForOne
                  ? applySqrtRatioBipsHundredthsDelta(assumedTruePriceAfterSwap, -feeAmount)
                  : applySqrtRatioBipsHundredthsDelta(assumedTruePriceAfterSwap, feeAmount)
                const {
                  amount0Delta: backrunDelta0,
                  amount1Delta: backrunDelta1,
                  executionPrice: backrunExecutionPrice,
                } = await simulateSwap(!zeroForOne, MaxUint256.div(2), priceToSwapTo)
                
                // Actual backrun swap (uses swapTarget)
                await pool.updateWhitelistedAddressForSwap(swapRouter.address)
                if (zeroForOne) { // Original user swap was 0->1 (price up), backrun is 1->0 (arb sells token1)
                  await swapToHigherPrice(priceToSwapTo, arbitrageur.address)
                } else { // Original user swap was 1->0 (price down), backrun is 0->1 (arb sells token0)
                  await swapToLowerPrice(priceToSwapTo, arbitrageur.address)
                }
                arbBalance0 = arbBalance0.sub(backrunDelta0)
                arbBalance1 = arbBalance1.sub(backrunDelta1)

                expect({
                  arbBalanceDelta0: formatTokenAmount(arbBalance0),
                  arbBalanceDelta1: formatTokenAmount(arbBalance1),
                  profit: {
                    final: formatTokenAmount(valueToken1(arbBalance0, arbBalance1)),
                  },
                  backrun: {
                    executionPrice: formatPrice(backrunExecutionPrice),
                    delta0: formatTokenAmount(backrunDelta0),
                    delta1: formatTokenAmount(backrunDelta1),
                  },
                  finalPrice: formatPrice((await pool.slot0()).sqrtPriceX96),
                }).to.matchSnapshot()
              })
            })
          }
        })
      }
    })
  }
})
