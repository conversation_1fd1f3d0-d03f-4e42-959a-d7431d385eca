import { <PERSON><PERSON><PERSON><PERSON>, Signer, ContractFactory } from 'ethers'
import { ethers } from 'hardhat'
import { MockTimeUniswapV3Pool } from '../../typechain/MockTimeUniswapV3Pool'
import { TestERC20 } from '../../typechain/TestERC20'
import { UniswapV3Factory } from '../../typechain/UniswapV3Factory'
import { OracleRegistry } from '../../typechain/OracleRegistry'
import { TestUniswapV3Callee } from '../../typechain/TestUniswapV3Callee'
import { TestUniswapV3Router } from '../../typechain/TestUniswapV3Router'
import { UniswapV3Pool } from '../../typechain/UniswapV3Pool'

import { Fixture } from 'ethereum-waffle'

interface FactoryFixture {
  factory: UniswapV3Factory
  oracleRegistry: OracleRegistry
  poolLogic: MockTimeUniswapV3Pool
}

async function factoryFixture(): Promise<FactoryFixture> {
  const wallets = await ethers.getSigners()
  const [wallet] = wallets
  const PoolLogicFactory = await ethers.getContractFactory('MockTimeUniswapV3Pool')
  const poolLogic = (await PoolLogicFactory.deploy()) as MockTimeUniswapV3Pool
  await poolLogic.deployed()

  const FactoryFactory = await ethers.getContractFactory('UniswapV3Factory')
  const factory = (await FactoryFactory.deploy()) as UniswapV3Factory
  await factory.deployed()

  const OracleRegistryFactory = await ethers.getContractFactory('OracleRegistry')
  const oracleRegistry = (await OracleRegistryFactory.deploy(factory.address)) as OracleRegistry
  await oracleRegistry.deployed()

  const MockVaultFactory = await ethers.getContractFactory('MockVault')
  const mockVault = await MockVaultFactory.deploy()
  await mockVault.deployed()

  await factory.initialize(wallet.address, oracleRegistry.address, poolLogic.address)
  await factory.setPeriphery(wallet.address)
  await factory.setVault(mockVault.address)
  
  return { factory, oracleRegistry, poolLogic }
}

interface TokensFixture {
  token0: TestERC20
  token1: TestERC20
  token2: TestERC20
}

async function tokensFixture(): Promise<TokensFixture> {
  const tokenFactory = await ethers.getContractFactory('TestERC20')
  const tokenA = (await tokenFactory.deploy(BigNumber.from(2).pow(255), 18)) as TestERC20
  const tokenB = (await tokenFactory.deploy(BigNumber.from(2).pow(255), 18)) as TestERC20
  const tokenC = (await tokenFactory.deploy(BigNumber.from(2).pow(255), 18)) as TestERC20

  await Promise.all([tokenA.deployed(), tokenB.deployed(), tokenC.deployed()])

  const [token0, token1, token2] = [tokenA, tokenB, tokenC].sort((_tokenA, _tokenB) =>
    _tokenA.address.toLowerCase() < _tokenB.address.toLowerCase() ? -1 : 1
  )

  return { token0, token1, token2 }
}

type TokensAndFactoryFixture = FactoryFixture & TokensFixture

interface PoolFixture extends TokensAndFactoryFixture {
  swapTargetCallee: TestUniswapV3Callee
  swapTargetRouter: TestUniswapV3Router
  MockTimeUniswapV3PoolLogicFactory: ContractFactory
  createPool(
    fee: number,
    tickSpacing: number,
    firstToken?: TestERC20,
    secondToken?: TestERC20,
    wallet?: Signer,
    launchParamsOverride?: any
  ): Promise<MockTimeUniswapV3Pool>
}

// Monday, October 5, 2020 9:00:00 AM GMT-05:00
export const TEST_POOL_START_TIME = **********

export const poolFixture: Fixture<PoolFixture> = async function (_wallets: Signer[], _provider): Promise<PoolFixture> {
  const [wallet] = await ethers.getSigners()
  const { factory, oracleRegistry, poolLogic } = await factoryFixture()
  const { token0, token1, token2 } = await tokensFixture()

  // Grant POOL_MANAGER_ROLE to all wallets provided to this fixture
  const POOL_MANAGER_ROLE = await factory.POOL_MANAGER_ROLE()
  for (const w of _wallets) {
    await factory.grantRole(POOL_MANAGER_ROLE, await w.getAddress())
  }

  const mockTimeUniswapV3PoolLogicFactoryInstance = await ethers.getContractFactory('MockTimeUniswapV3Pool')

  const calleeContractFactory = await ethers.getContractFactory('TestUniswapV3Callee')
  const swapTargetCallee = (await calleeContractFactory.deploy()) as TestUniswapV3Callee
  await swapTargetCallee.deployed()

  const routerContractFactory = await ethers.getContractFactory('TestUniswapV3Router')
  const swapTargetRouter = (await routerContractFactory.deploy()) as TestUniswapV3Router
  await swapTargetRouter.deployed()

  return {
    token0,
    token1,
    token2,
    factory,
    oracleRegistry,
    poolLogic,
    swapTargetCallee,
    swapTargetRouter,
    MockTimeUniswapV3PoolLogicFactory: mockTimeUniswapV3PoolLogicFactoryInstance,
    createPool: async (fee, tickSpacing, firstToken = token0, secondToken = token1, _wallet = wallet, launchParamsOverride) => {
      // Deploy mock Chainlink oracles for both tokens
      const MockChainlinkOracleFactory = await ethers.getContractFactory('MockChainlinkOracle');
      const mockOracle0 = await MockChainlinkOracleFactory.deploy(ethers.utils.parseUnits('1', 8)); // 1 USD, 8 decimals
      await mockOracle0.deployed();
      const mockOracle1 = await MockChainlinkOracleFactory.deploy(ethers.utils.parseUnits('1', 8)); // 1 USD, 8 decimals
      await mockOracle1.deployed();
      // Set oracle for both tokens to the mock oracles
      await factory.setOracle(firstToken.address, mockOracle0.address);
      await factory.setOracle(secondToken.address, mockOracle1.address);

      // Always grant POOL_MANAGER_ROLE to the _wallet before calling createPool
      const POOL_MANAGER_ROLE = await factory.POOL_MANAGER_ROLE()
      const callerAddress = typeof _wallet === 'string' ? _wallet : await _wallet.getAddress()
      await factory.grantRole(POOL_MANAGER_ROLE, callerAddress)
      // If launchParamsOverride.projectManager is set and is a different address, grant role to it too
      if (launchParamsOverride && launchParamsOverride.projectManager && launchParamsOverride.projectManager !== callerAddress) {
        await factory.grantRole(POOL_MANAGER_ROLE, launchParamsOverride.projectManager)
      }

      const now = Math.floor(Date.now() / 1000)
      const launchParams = {
        tokenLaunchType: 0,
        exclusiveTradingPeriodStart: 0,
        exclusiveTradingPeriodEnd: 0,
        extendedLiquidityLockDuration: 0,
        projectManager: (launchParamsOverride && launchParamsOverride.projectManager) || callerAddress,
        whitelistedAddressForSwap: (launchParamsOverride && launchParamsOverride.whitelistedAddressForSwap) || swapTargetCallee.address,
        ...launchParamsOverride
      }

      const tx = await factory.connect(_wallet).createPool(
        firstToken.address,
        secondToken.address,
        fee,
        launchParams
      )

      const receipt = await tx.wait()
      const poolCreatedEvent = receipt.events?.find((e: any) => e.event === 'PoolCreated')
      const poolAddress = poolCreatedEvent?.args?.pool

      if (!poolAddress) {
        throw new Error('Pool deployment failed or event not found')
      }

      const pool = mockTimeUniswapV3PoolLogicFactoryInstance.attach(poolAddress) as MockTimeUniswapV3Pool
      await pool.setInitialTime()
      return pool
    },
  }
}