name: Build and Test

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest

    env:
      ALCHEMY_API_KEY: ${{ secrets.ALCHEMY_API_KEY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Compile
        run: yarn compile

      - name: Run tests
        run: yarn test