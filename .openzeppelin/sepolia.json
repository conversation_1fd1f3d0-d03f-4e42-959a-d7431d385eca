{"manifestVersion": "3.2", "admin": {"address": "0x3E40759D65ED687BB778e18055EF599e18633c21", "txHash": "0x7ece68e305758c384256dba39d828ff088a5e4cb5e5f044ddaeb1d72f6be62ef"}, "proxies": [{"address": "0x5113fC3C580d971BffA0F1d0D93723eAF721Fa85", "txHash": "0x5c622dffa73899c2bba92f3138fbe68b98cb92f32da2e516c9f0d477fd8dac04", "kind": "transparent"}, {"address": "0x3D4c4d31aD560e04a3849993CaDe4428b5b88DbA", "txHash": "0x910b70008224c5ff7abbf39bcdbb231a63dfaad5e96139de5677037af0fa9810", "kind": "transparent"}, {"address": "0xEf6Cfd46D0EA5B3fed473419c4E3F54664a7B30b", "txHash": "0x285b0a8dde2f3dc553f16b2775b2691c47dfe821fce53377f24fc3c38a93e29c", "kind": "transparent"}, {"address": "0x22bdc9206EA1E9d96400763dd16Dbc2854c72aa2", "txHash": "0x33e750d989f964394ecc35646f07258dd54885359b6443fb9332eefecc3e320e", "kind": "transparent"}, {"address": "0x96232BC4a7b4bA7173662710a7D214ea442f761D", "txHash": "0x78e0e2425542e0ac8ec201f145793fd737ff7b391fd6b3cd77786cc1d01d6f17", "kind": "transparent"}, {"address": "0x89e4624E10515aB69746B52628399724c8c56BC1", "txHash": "0xacb790b1ea3ce6a259542f651ed1d2dbd1980b887e4ca6b01559e9fe94fe5912", "kind": "transparent"}, {"address": "0x079cA6D349480c5C1c3EDCE7bD90f16CACEEe5cE", "txHash": "0x026457e9d6253c3a2ed109c4ef3e34b94d009494eec4975d939e484697b472a1", "kind": "transparent"}, {"address": "0x2eECd9c73198D17b60b5E67Fbf6A5A5E6b1fd1DB", "txHash": "0xf31ec25191516a4d095bac4ffb43c3c77a7bfa6fdff92db3a3315544b36e6be0", "kind": "transparent"}, {"address": "0x55CdFA185376e329CB44c4df076CD6B6b2C0e0e5", "txHash": "0x85ac032c6e7874aff3a29f72717185309fd9b04994814d2fb9bffa045b17d7a0", "kind": "transparent"}, {"address": "0x40720c4af702b0f34F19A171ebF48951F0Af2D17", "txHash": "0x70a409dae3be80d45313b3e5c568fd30894b910dd9db5467c78b275986b0a844", "kind": "transparent"}, {"address": "0x97ba01326084E8d20E730abb220f842f8BfdF960", "txHash": "0xcd2b70a8943b4d7705d52fb734dc10e7b74d1876aa6df8fbbd37f723aa937239", "kind": "transparent"}, {"address": "0x060459E9DAA09e3D204495069Db83f60d7fbE2a8", "txHash": "0xa0179584ad6026f3e799eabd7338dd8624ddfdf4c1d24efc8c4f9115fdca69cb", "kind": "transparent"}, {"address": "0xBEb96918B6Cd4292ee1bD468317F8bBf01E046Ba", "txHash": "0xd77ce5c8039370f3c898319e90b61133810f99b417583ea770f99c25905a2c72", "kind": "transparent"}, {"address": "0x56D7F9505D71657429682BE34467243AECF7E4D7", "txHash": "0x3af048b5aea5d705ae38b00a5c706ef89416bb618f8671bd777f5cd67f39898c", "kind": "transparent"}, {"address": "0x56Fd310789EBD23F1a481Ad427e22c58309E5D95", "txHash": "0x65abd710e454619be2ff284ac72e37a1a9fbebcb796bba942f9d9ed1016ca4c7", "kind": "transparent"}, {"address": "0xed9973e88Faf0F44D5394Ec07Ea580d9da6e0e14", "txHash": "0xe347648c595cf4b73d4df267f26ea15a48ed110f2b71ebc6339cd877c4eaa9a6", "kind": "transparent"}, {"address": "0x80ee48727784e015fb0f3CBed8f7B896793DbF89", "txHash": "0x268a0566a4ce0a696805af663c752a44a7d4d246352e7195546776a5943a6f8a", "kind": "transparent"}, {"address": "0xd1D2608301087b2265c9cfE952498A9B30e29F11", "txHash": "0x382224de8ae4020373a1501211132a24b95701f8b959fb382d7a84ec101f546e", "kind": "transparent"}, {"address": "0x9213aF8f1441c9443c4Fe9DE8FF978e4BB6d8D9b", "txHash": "0xbf3ad9ecc992ddd3bd9779862933025e5e37417e02c690e769892deaecde8b1f", "kind": "transparent"}, {"address": "0xcFCEb1d29a9389d4a546700ea99d1FdfB1AEcE8F", "txHash": "0x11a55e889656ccefbb38f45c4679746b0ff461a3b73dc65b106cce5da2b59ce9", "kind": "transparent"}, {"address": "0xdC4b9A95Cb8C788090E78Ea323a98C899cD8B209", "txHash": "0x3c2c429d97d14afa0850ec4290d994aeeb29daf0208a2a04976d07351c1b72a2", "kind": "transparent"}, {"address": "0xB21bC2Fe772485e3D1d22A22bdebd1028eD64e9D", "txHash": "0xfdb48dbde018480ad43db1c2b76045751c8a2c99cb7df38c462863f0b5f645a7", "kind": "transparent"}, {"address": "0x0206001e08EfBCb5011562efcCc383b621277F41", "txHash": "0xf291d1606f30042a3754c0c37c592e4e1e41f68032bf05331faf743d30dcd727", "kind": "transparent"}, {"address": "0x30C1093bBfFF2DEb0517559889dEfCfc14bDDad1", "txHash": "0xc33d43c4795a44706c6dc6938916901cbc5db80b5b621c156daafbb5334ff2d5", "kind": "transparent"}, {"address": "0xade2be2BA5dEaDDC2DFa1D5D77Cfe4Fc23F0c583", "txHash": "0x3850e3f89fce19f3675e46e963ca89bbd2a60bf45df16bd4cde0947e6637ad37", "kind": "transparent"}, {"address": "0x500C16B10849Ba6b34c70723bF823e2a3dD219eb", "txHash": "0xb795164b45ec1e0c89bb159a4be546bff5e07b3e3cf1447df6231f030652e9bd", "kind": "transparent"}, {"address": "0xd4a697770dd377d3DD8B6ea3CB5CC2b5766b03aA", "txHash": "0xccd9579ea8e0940bc49dd3724af674dfa314191579a3dda8fb427d53a960fa14", "kind": "transparent"}], "impls": {"8a062d22d0acaf4bfb4f5ddcda047cfca81a3b3e1ea2f84e20fddfbe69b888bc": {"address": "0x230dE761Ad5D319907dD7d6bf2d5e79cdA25025c", "txHash": "0x47bbccdc424c13a49bcc4855da931b68606ab39d32ae80f810614a2599d17024", "layout": {"solcVersion": "0.7.6", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:25"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:30"}, {"label": "oracleRegistry", "offset": 2, "slot": "0", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:17"}, {"label": "owner", "offset": 0, "slot": "1", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:20"}, {"label": "poolLogicContract", "offset": 0, "slot": "2", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:23"}, {"label": "poolBeacon", "offset": 0, "slot": "3", "type": "t_contract(UpgradeableBeacon)720", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:26"}, {"label": "feeAmountTickSpacing", "offset": 0, "slot": "4", "type": "t_mapping(t_uint24,t_int24)", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:29"}, {"label": "getPool", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:31"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_contract(UpgradeableBeacon)720": {"label": "contract UpgradeableBeacon", "numberOfBytes": "20"}, "t_int24": {"label": "int24", "numberOfBytes": "3"}, "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))": {"label": "mapping(address => mapping(address => mapping(uint24 => address)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint24,t_address))": {"label": "mapping(address => mapping(uint24 => address))", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_address)": {"label": "mapping(uint24 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_int24)": {"label": "mapping(uint24 => int24)", "numberOfBytes": "32"}, "t_uint24": {"label": "uint24", "numberOfBytes": "3"}}, "namespaces": {}}}, "ecae8b650894d3155dfb9fb0d1a9d3b33ce4a9884c6547ffbf463b28ec752c97": {"address": "0x6E283F6ddd6e863E6d70E947133Ef5cf2c231e38", "txHash": "0xc1a215cdd5aec26301777ecf1f4857714944e4232c5d8acc00bb90c2ed82b787", "layout": {"solcVersion": "0.7.6", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:25"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:30"}, {"label": "_roles", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(RoleData)373_storage)", "contract": "AccessControl", "src": "@openzeppelin/contracts/access/AccessControl.sol:53"}, {"label": "oracleRegistry", "offset": 0, "slot": "2", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:23"}, {"label": "poolLogicContract", "offset": 0, "slot": "3", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:26"}, {"label": "poolBeacon", "offset": 0, "slot": "4", "type": "t_contract(UpgradeableBeacon)1003", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:29"}, {"label": "feeAmountTickSpacing", "offset": 0, "slot": "5", "type": "t_mapping(t_uint24,t_int24)", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:32"}, {"label": "getPool", "offset": 0, "slot": "6", "type": "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:34"}, {"label": "__gap", "offset": 0, "slot": "7", "type": "t_array(t_uint256)50_storage", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:145"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(UpgradeableBeacon)1003": {"label": "contract UpgradeableBeacon", "numberOfBytes": "20"}, "t_int24": {"label": "int24", "numberOfBytes": "3"}, "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))": {"label": "mapping(address => mapping(address => mapping(uint24 => address)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint24,t_address))": {"label": "mapping(address => mapping(uint24 => address))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)373_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_address)": {"label": "mapping(uint24 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_int24)": {"label": "mapping(uint24 => int24)", "numberOfBytes": "32"}, "t_struct(AddressSet)1598_storage": {"label": "struct EnumerableSet.AddressSet", "members": [{"label": "_inner", "type": "t_struct(Set)1333_storage", "offset": 0, "slot": "0"}], "numberOfBytes": "64"}, "t_struct(RoleData)373_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_struct(AddressSet)1598_storage", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_struct(Set)1333_storage": {"label": "struct EnumerableSet.Set", "members": [{"label": "_values", "type": "t_array(t_bytes32)dyn_storage", "offset": 0, "slot": "0"}, {"label": "_indexes", "type": "t_mapping(t_bytes32,t_uint256)", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint24": {"label": "uint24", "numberOfBytes": "3"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {}}}, "b8f37792b5103b866e7ff7bda4a3bd6f18c6b04dd5ed6f065a7b0d989e2b8599": {"address": "0xe1D076EB520A49C728beB4aE490D470fA7f6FBa7", "txHash": "0xe202b3fd05278c805a8f4e263bb57c544e2cb43e4dc46626df087c72b208240d", "layout": {"solcVersion": "0.7.6", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:25"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:30"}, {"label": "_roles", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(RoleData)373_storage)", "contract": "AccessControl", "src": "@openzeppelin/contracts/access/AccessControl.sol:53"}, {"label": "oracleRegistry", "offset": 0, "slot": "2", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:23"}, {"label": "poolLogicContract", "offset": 0, "slot": "3", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:26"}, {"label": "poolBeacon", "offset": 0, "slot": "4", "type": "t_contract(UpgradeableBeacon)1003", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:29"}, {"label": "feeAmountTickSpacing", "offset": 0, "slot": "5", "type": "t_mapping(t_uint24,t_int24)", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:32"}, {"label": "getPool", "offset": 0, "slot": "6", "type": "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:34"}, {"label": "__gap", "offset": 0, "slot": "7", "type": "t_array(t_uint256)50_storage", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:145"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(UpgradeableBeacon)1003": {"label": "contract UpgradeableBeacon", "numberOfBytes": "20"}, "t_int24": {"label": "int24", "numberOfBytes": "3"}, "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))": {"label": "mapping(address => mapping(address => mapping(uint24 => address)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint24,t_address))": {"label": "mapping(address => mapping(uint24 => address))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)373_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_address)": {"label": "mapping(uint24 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_int24)": {"label": "mapping(uint24 => int24)", "numberOfBytes": "32"}, "t_struct(AddressSet)1598_storage": {"label": "struct EnumerableSet.AddressSet", "members": [{"label": "_inner", "type": "t_struct(Set)1333_storage", "offset": 0, "slot": "0"}], "numberOfBytes": "64"}, "t_struct(RoleData)373_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_struct(AddressSet)1598_storage", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_struct(Set)1333_storage": {"label": "struct EnumerableSet.Set", "members": [{"label": "_values", "type": "t_array(t_bytes32)dyn_storage", "offset": 0, "slot": "0"}, {"label": "_indexes", "type": "t_mapping(t_bytes32,t_uint256)", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint24": {"label": "uint24", "numberOfBytes": "3"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {}}}, "93c30e08ac785c30f4c21dfcc8b37d743080e626ad3754bc3075b01152e5de30": {"address": "0x874D7aaDF611897fF5Eb612ab01e874fff7A4F8e", "txHash": "0xf63577b49a771d75be414f890538a52f151e313dd82bfea884a5b442562fcdab", "layout": {"solcVersion": "0.7.6", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:25"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:30"}, {"label": "_roles", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(RoleData)373_storage)", "contract": "AccessControl", "src": "@openzeppelin/contracts/access/AccessControl.sol:53"}, {"label": "oracleRegistry", "offset": 0, "slot": "2", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:23"}, {"label": "poolLogicContract", "offset": 0, "slot": "3", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:26"}, {"label": "poolBeacon", "offset": 0, "slot": "4", "type": "t_contract(UpgradeableBeacon)2053", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:29"}, {"label": "periphery", "offset": 0, "slot": "5", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:32"}, {"label": "feeAmountTickSpacing", "offset": 0, "slot": "6", "type": "t_mapping(t_uint24,t_int24)", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:35"}, {"label": "getPool", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:37"}, {"label": "__gap", "offset": 0, "slot": "8", "type": "t_array(t_uint256)50_storage", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:148"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(UpgradeableBeacon)2053": {"label": "contract UpgradeableBeacon", "numberOfBytes": "20"}, "t_int24": {"label": "int24", "numberOfBytes": "3"}, "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))": {"label": "mapping(address => mapping(address => mapping(uint24 => address)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint24,t_address))": {"label": "mapping(address => mapping(uint24 => address))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)373_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_address)": {"label": "mapping(uint24 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_int24)": {"label": "mapping(uint24 => int24)", "numberOfBytes": "32"}, "t_struct(AddressSet)4975_storage": {"label": "struct EnumerableSet.AddressSet", "members": [{"label": "_inner", "type": "t_struct(Set)4710_storage", "offset": 0, "slot": "0"}], "numberOfBytes": "64"}, "t_struct(RoleData)373_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_struct(AddressSet)4975_storage", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_struct(Set)4710_storage": {"label": "struct EnumerableSet.Set", "members": [{"label": "_values", "type": "t_array(t_bytes32)dyn_storage", "offset": 0, "slot": "0"}, {"label": "_indexes", "type": "t_mapping(t_bytes32,t_uint256)", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint24": {"label": "uint24", "numberOfBytes": "3"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {}}}, "3ffe5b8ed2225d1022e35d13b9e14e58319b5ba6c12237e152ec0a1f147b06f6": {"address": "0x42B74180178427c6c956E1D23C541a1989A78E0F", "txHash": "0xe041d6b96202d3066733b596dd10deffb38a7c9b748d63ecd5c974893f464c9f", "layout": {"solcVersion": "0.7.6", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:25"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:30"}, {"label": "_roles", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(RoleData)373_storage)", "contract": "AccessControl", "src": "@openzeppelin/contracts/access/AccessControl.sol:53"}, {"label": "oracleRegistry", "offset": 0, "slot": "2", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:45"}, {"label": "poolLogicContract", "offset": 0, "slot": "3", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:48"}, {"label": "poolBeacon", "offset": 0, "slot": "4", "type": "t_contract(UpgradeableBeacon)2053", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:51"}, {"label": "periphery", "offset": 0, "slot": "5", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:54"}, {"label": "feeAmountTickSpacing", "offset": 0, "slot": "6", "type": "t_mapping(t_uint24,t_int24)", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:57"}, {"label": "getPool", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:59"}, {"label": "vault", "offset": 0, "slot": "8", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:62"}, {"label": "__gap", "offset": 0, "slot": "9", "type": "t_array(t_uint256)49_storage", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:221"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(UpgradeableBeacon)2053": {"label": "contract UpgradeableBeacon", "numberOfBytes": "20"}, "t_int24": {"label": "int24", "numberOfBytes": "3"}, "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))": {"label": "mapping(address => mapping(address => mapping(uint24 => address)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint24,t_address))": {"label": "mapping(address => mapping(uint24 => address))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)373_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_address)": {"label": "mapping(uint24 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_int24)": {"label": "mapping(uint24 => int24)", "numberOfBytes": "32"}, "t_struct(AddressSet)4975_storage": {"label": "struct EnumerableSet.AddressSet", "members": [{"label": "_inner", "type": "t_struct(Set)4710_storage", "offset": 0, "slot": "0"}], "numberOfBytes": "64"}, "t_struct(RoleData)373_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_struct(AddressSet)4975_storage", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_struct(Set)4710_storage": {"label": "struct EnumerableSet.Set", "members": [{"label": "_values", "type": "t_array(t_bytes32)dyn_storage", "offset": 0, "slot": "0"}, {"label": "_indexes", "type": "t_mapping(t_bytes32,t_uint256)", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint24": {"label": "uint24", "numberOfBytes": "3"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {}}}}}