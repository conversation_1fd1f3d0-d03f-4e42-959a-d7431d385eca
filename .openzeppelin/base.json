{"manifestVersion": "3.2", "admin": {"address": "0x65EB92f72D31Ec39d87b412FD00d8359aC78571c", "txHash": "0x8534e21447cde8377d1b6c7719956b40a78904fb696852de3b1dc020bf826112"}, "proxies": [{"address": "0x6b4e873440753b0011446C2a66dfF0fDDb550991", "txHash": "0x575ad1627ada1a39f5ab0a211e044608314565336aa3f17f85c7cd799b8f4c44", "kind": "transparent"}, {"address": "0xfBBCfF4D80a0ed25Ac78B05144A1E6d2B2A4f8B6", "txHash": "0x84c4758ea2230fb48254784c1b39c9838b218624f7147a646dbc8244a22c359b", "kind": "transparent"}, {"address": "0x9926Ee3B756d05486820234ABcD23B810ee37895", "txHash": "0xd28c8609c40f792b3fe22644fc1b7b89cef7f6c4520c46dd41f1bd48e1ce8f38", "kind": "transparent"}, {"address": "0xDC4A1639E23AE106ABCdB58aB57f807ce9438E1c", "txHash": "0x19ea45e310d6c872c4014e6aefd79a1e1ce43e0b332fa11633d48d4e422eaa97", "kind": "transparent"}, {"address": "0xC4807E33650F63A2551dE2cFB5eE06C5E5C04cD6", "txHash": "0xc52aa666020d09de72bf67c44d2bdbe494abdce61591a627f61370236378f21e", "kind": "transparent"}], "impls": {"b8f37792b5103b866e7ff7bda4a3bd6f18c6b04dd5ed6f065a7b0d989e2b8599": {"address": "0x7FC3cC51949D33903587545b364c53272e028455", "txHash": "0xd0cfb8b48e7c6cfacd815cf49104b14eb9be7fb3d00a5e37721613956705645d", "layout": {"solcVersion": "0.7.6", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:25"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:30"}, {"label": "_roles", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(RoleData)373_storage)", "contract": "AccessControl", "src": "@openzeppelin/contracts/access/AccessControl.sol:53"}, {"label": "oracleRegistry", "offset": 0, "slot": "2", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:23"}, {"label": "poolLogicContract", "offset": 0, "slot": "3", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:26"}, {"label": "poolBeacon", "offset": 0, "slot": "4", "type": "t_contract(UpgradeableBeacon)1003", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:29"}, {"label": "feeAmountTickSpacing", "offset": 0, "slot": "5", "type": "t_mapping(t_uint24,t_int24)", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:32"}, {"label": "getPool", "offset": 0, "slot": "6", "type": "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:34"}, {"label": "__gap", "offset": 0, "slot": "7", "type": "t_array(t_uint256)50_storage", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:145"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(UpgradeableBeacon)1003": {"label": "contract UpgradeableBeacon", "numberOfBytes": "20"}, "t_int24": {"label": "int24", "numberOfBytes": "3"}, "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))": {"label": "mapping(address => mapping(address => mapping(uint24 => address)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint24,t_address))": {"label": "mapping(address => mapping(uint24 => address))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)373_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_address)": {"label": "mapping(uint24 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_int24)": {"label": "mapping(uint24 => int24)", "numberOfBytes": "32"}, "t_struct(AddressSet)1598_storage": {"label": "struct EnumerableSet.AddressSet", "members": [{"label": "_inner", "type": "t_struct(Set)1333_storage", "offset": 0, "slot": "0"}], "numberOfBytes": "64"}, "t_struct(RoleData)373_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_struct(AddressSet)1598_storage", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_struct(Set)1333_storage": {"label": "struct EnumerableSet.Set", "members": [{"label": "_values", "type": "t_array(t_bytes32)dyn_storage", "offset": 0, "slot": "0"}, {"label": "_indexes", "type": "t_mapping(t_bytes32,t_uint256)", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint24": {"label": "uint24", "numberOfBytes": "3"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {}}}, "93c30e08ac785c30f4c21dfcc8b37d743080e626ad3754bc3075b01152e5de30": {"address": "0x166fB403daC3E1343C5D53BA31998F3Cc5673a47", "txHash": "0x9992ddf80b452d8919428b29b01abc7f6cc62e84138b4818c31d34552fcea38c", "layout": {"solcVersion": "0.7.6", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:25"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:30"}, {"label": "_roles", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(RoleData)373_storage)", "contract": "AccessControl", "src": "@openzeppelin/contracts/access/AccessControl.sol:53"}, {"label": "oracleRegistry", "offset": 0, "slot": "2", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:23"}, {"label": "poolLogicContract", "offset": 0, "slot": "3", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:26"}, {"label": "poolBeacon", "offset": 0, "slot": "4", "type": "t_contract(UpgradeableBeacon)2053", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:29"}, {"label": "periphery", "offset": 0, "slot": "5", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:32"}, {"label": "feeAmountTickSpacing", "offset": 0, "slot": "6", "type": "t_mapping(t_uint24,t_int24)", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:35"}, {"label": "getPool", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:37"}, {"label": "__gap", "offset": 0, "slot": "8", "type": "t_array(t_uint256)50_storage", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:148"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(UpgradeableBeacon)2053": {"label": "contract UpgradeableBeacon", "numberOfBytes": "20"}, "t_int24": {"label": "int24", "numberOfBytes": "3"}, "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))": {"label": "mapping(address => mapping(address => mapping(uint24 => address)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint24,t_address))": {"label": "mapping(address => mapping(uint24 => address))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)373_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_address)": {"label": "mapping(uint24 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_int24)": {"label": "mapping(uint24 => int24)", "numberOfBytes": "32"}, "t_struct(AddressSet)4975_storage": {"label": "struct EnumerableSet.AddressSet", "members": [{"label": "_inner", "type": "t_struct(Set)4710_storage", "offset": 0, "slot": "0"}], "numberOfBytes": "64"}, "t_struct(RoleData)373_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_struct(AddressSet)4975_storage", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_struct(Set)4710_storage": {"label": "struct EnumerableSet.Set", "members": [{"label": "_values", "type": "t_array(t_bytes32)dyn_storage", "offset": 0, "slot": "0"}, {"label": "_indexes", "type": "t_mapping(t_bytes32,t_uint256)", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint24": {"label": "uint24", "numberOfBytes": "3"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {}}}, "3ffe5b8ed2225d1022e35d13b9e14e58319b5ba6c12237e152ec0a1f147b06f6": {"address": "0x99DF49A7EF9c2D0BC06fBD5e3105E4057E290e51", "txHash": "0xd0acab8f58e4f440da576335fbe41d941ffbab55e2cda194481956528fc749d8", "layout": {"solcVersion": "0.7.6", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:25"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/Initializable.sol:30"}, {"label": "_roles", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_struct(RoleData)373_storage)", "contract": "AccessControl", "src": "@openzeppelin/contracts/access/AccessControl.sol:53"}, {"label": "oracleRegistry", "offset": 0, "slot": "2", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:45"}, {"label": "poolLogicContract", "offset": 0, "slot": "3", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:48"}, {"label": "poolBeacon", "offset": 0, "slot": "4", "type": "t_contract(UpgradeableBeacon)2053", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:51"}, {"label": "periphery", "offset": 0, "slot": "5", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:54"}, {"label": "feeAmountTickSpacing", "offset": 0, "slot": "6", "type": "t_mapping(t_uint24,t_int24)", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:57"}, {"label": "getPool", "offset": 0, "slot": "7", "type": "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:59"}, {"label": "vault", "offset": 0, "slot": "8", "type": "t_address", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:62"}, {"label": "__gap", "offset": 0, "slot": "9", "type": "t_array(t_uint256)49_storage", "contract": "UniswapV3Factory", "src": "contracts/UniswapV3Factory.sol:221"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(UpgradeableBeacon)2053": {"label": "contract UpgradeableBeacon", "numberOfBytes": "20"}, "t_int24": {"label": "int24", "numberOfBytes": "3"}, "t_mapping(t_address,t_mapping(t_address,t_mapping(t_uint24,t_address)))": {"label": "mapping(address => mapping(address => mapping(uint24 => address)))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint24,t_address))": {"label": "mapping(address => mapping(uint24 => address))", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)373_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_address)": {"label": "mapping(uint24 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint24,t_int24)": {"label": "mapping(uint24 => int24)", "numberOfBytes": "32"}, "t_struct(AddressSet)4975_storage": {"label": "struct EnumerableSet.AddressSet", "members": [{"label": "_inner", "type": "t_struct(Set)4710_storage", "offset": 0, "slot": "0"}], "numberOfBytes": "64"}, "t_struct(RoleData)373_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_struct(AddressSet)4975_storage", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_struct(Set)4710_storage": {"label": "struct EnumerableSet.Set", "members": [{"label": "_values", "type": "t_array(t_bytes32)dyn_storage", "offset": 0, "slot": "0"}, {"label": "_indexes", "type": "t_mapping(t_bytes32,t_uint256)", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint24": {"label": "uint24", "numberOfBytes": "3"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "namespaces": {}}}}}