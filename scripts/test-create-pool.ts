import hre from "hardhat";
import { Contract, BigNumber } from "ethers";
import * as dotenv from "dotenv";
dotenv.config();


const FACTORY_ADDRESS = "******************************************";

const TOKEN_A_MINT_AMOUNT = hre.ethers.utils.parseEther("1000000"); // 1 million Token A
const TOKEN_B_MINT_AMOUNT = hre.ethers.utils.parseEther("1000000"); // 1 million Token B

const POOL_FEE_TIER = 3000; // 0.3%
const INITIAL_SQRT_PRICE_X96 = BigNumber.from("79228162514264337593543950336"); // Represents a 1:1 price (2^96)

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("Using account:", deployer.address, "on network:", hre.network.name);

    if (!hre.ethers.utils.isAddress(FACTORY_ADDRESS)) {
        console.error("Invalid FACTORY_ADDRESS in scripts/create-pool.ts");
        process.exit(1);
    }

    // 1. Get Factory instance
    const factory = await hre.ethers.getContractAt("IUniswapV3Factory", FACTORY_ADDRESS, deployer);
    console.log("UniswapV3Factory instance fetched from:", factory.address);

    // 2. Deploy Mock ERC20 tokens
    const TestERC20 = await hre.ethers.getContractFactory("TestERC20", deployer);

    console.log("Deploying MockTokenA...");
    const tokenA = await TestERC20.deploy(TOKEN_A_MINT_AMOUNT);
    await tokenA.deployed();
    console.log("MockTokenA deployed to:", tokenA.address);

    console.log("Deploying MockTokenB...");
    const tokenB = await TestERC20.deploy(TOKEN_B_MINT_AMOUNT);
    await tokenB.deployed();
    console.log("MockTokenB deployed to:", tokenB.address);

    // Ensure token0 is the lower address for createPool
    const [token0Address, token1Address] = tokenA.address.toLowerCase() < tokenB.address.toLowerCase()
        ? [tokenA.address, tokenB.address]
        : [tokenB.address, tokenA.address];
    
    console.log(`Token0: ${token0Address}, Token1: ${token1Address}, Fee: ${POOL_FEE_TIER}`);

    // 3. Create the pool via the factory
    console.log("Creating pool...");
    const createPoolTx = await factory.createPool(token0Address, token1Address, POOL_FEE_TIER);
    const receipt = await createPoolTx.wait();
    
    const poolCreatedEvent = receipt.events?.find((event: any) => event.event === "PoolCreated");
    const poolAddress = poolCreatedEvent?.args?.pool;

    if (!poolAddress || poolAddress === hre.ethers.constants.AddressZero) {
        console.error("Failed to retrieve pool address from PoolCreated event.");
        process.exit(1);
    }

    console.log(`UniswapV3Pool (Beacon Proxy) created at: ${poolAddress}`);

    const newPool = await hre.ethers.getContractAt("IUniswapV3Pool", poolAddress, deployer);
    console.log(`Initializing pool ${poolAddress} with sqrtPriceX96: ${INITIAL_SQRT_PRICE_X96.toString()}...`);
    
    const initializeTx = await newPool.initialize(INITIAL_SQRT_PRICE_X96);
    await initializeTx.wait();
    console.log(`Pool ${poolAddress} initialized successfully.`);

    const slot0 = await newPool.slot0();
    console.log(`Initial tick: ${slot0.tick}, sqrtPriceX96: ${slot0.sqrtPriceX96.toString()}`);

    console.log("\n--- Deployed Contracts ---");
    console.log("MockTokenA:", tokenA.address);
    console.log("MockTokenB:", tokenB.address);
    console.log("UniswapV3Pool Proxy:", poolAddress);
    console.log("   (points to beacon controlled by factory at:", factory.address, ")");
    console.log(`   To interact with the pool, use ABI for IUniswapV3Pool/UniswapV3Pool at address ${poolAddress}`);

}

main()
    .then(() => process.exit(0))
    .catch(error => {
        console.error(error);
        process.exit(1);
    }); 