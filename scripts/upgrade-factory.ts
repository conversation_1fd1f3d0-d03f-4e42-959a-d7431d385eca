import hre from "hardhat";
import { Contract } from "ethers";
import * as dotenv from "dotenv";
dotenv.config(); 

const FACTORY_PROXY_ADDRESS = "******************************************";

async function main() {
  const NewFactoryLogic = await hre.ethers.getContractFactory("UniswapV3Factory"); 
  console.log("Preparing to upgrade UniswapV3Factory proxy at:", FACTORY_PROXY_ADDRESS);

  const factoryProxy: Contract = await hre.upgrades.upgradeProxy(FACTORY_PROXY_ADDRESS, NewFactoryLogic, {
    kind: "transparent",
  });

  console.log("UniswapV3Factory Proxy (at ", factoryProxy.address, ") upgrade transaction sent.");
  await factoryProxy.deployed(); 
  console.log("Upgrade process completed.")

  const newImplementationAddress = await hre.upgrades.erc1967.getImplementationAddress(factoryProxy.address);
  console.log("New UniswapV3Factory Implementation is at:", newImplementationAddress);

}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 