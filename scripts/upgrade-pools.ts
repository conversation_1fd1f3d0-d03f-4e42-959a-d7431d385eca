import hre from "hardhat";
import { Contract } from "ethers";
import * as dotenv from "dotenv";
dotenv.config();

// --- CONFIGURATION ---
// Replace with your deployed UniswapV3Factory address on the target network
const FACTORY_ADDRESS = "******************************************"; // Sepolia Factory

// If you have a V2 pool logic contract, specify its name here.
// For this example, we'll just re-deploy the existing UniswapV3Pool
// and treat it as if it were a "V2" for demonstration purposes.
const NEW_POOL_LOGIC_CONTRACT_NAME = "UniswapV3Pool"; 

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("Using account:", deployer.address, "on network:", hre.network.name);

    if (!hre.ethers.utils.isAddress(FACTORY_ADDRESS)) {
        console.error("Invalid FACTORY_ADDRESS in scripts/upgrade-pools.ts");
        process.exit(1);
    }

    // 1. Get Factory instance
    const factory = await hre.ethers.getContractAt("IUniswapV3Factory", FACTORY_ADDRESS, deployer);
    console.log("UniswapV3Factory instance fetched from:", factory.address);

    const oldPoolLogicAddress = await factory.poolLogicContract();
    console.log("Current Pool Logic Contract:", oldPoolLogicAddress);

    // 2. Deploy the new Pool Logic Contract
    console.log(`Deploying new Pool Logic Contract (${NEW_POOL_LOGIC_CONTRACT_NAME})...`);
    const NewPoolLogicFactory = await hre.ethers.getContractFactory(NEW_POOL_LOGIC_CONTRACT_NAME, deployer);
    const newPoolLogic = await NewPoolLogicFactory.deploy();
    await newPoolLogic.deployed();
    console.log(`New Pool Logic (${NEW_POOL_LOGIC_CONTRACT_NAME}) deployed to:`, newPoolLogic.address);

    if (newPoolLogic.address === oldPoolLogicAddress) {
        console.warn("New pool logic address is the same as the old one. No effective upgrade will occur.");

    }

    // 3. Upgrade the factory's poolBeacon to point to the new pool logic
    console.log(`Calling setPoolLogicContract on factory to upgrade beacon to ${newPoolLogic.address}...`);
    const upgradeTx = await factory.setPoolLogicContract(newPoolLogic.address);
    await upgradeTx.wait();
    console.log("Factory's setPoolLogicContract transaction mined.");

    const veryNewPoolLogicAddress = await factory.poolLogicContract();
    if (veryNewPoolLogicAddress === newPoolLogic.address) {
        console.log("Successfully upgraded pool logic in factory to:", veryNewPoolLogicAddress);
    } else {
        console.error("ERROR: Pool logic address in factory does not match new deployment! Current:", veryNewPoolLogicAddress);
    }
    
    console.log("\n--- Summary ---");
    console.log("Factory Address:", factory.address);
    console.log("Old Pool Logic Address:", oldPoolLogicAddress);
    console.log("New Pool Logic Address (deployed & set in factory):", newPoolLogic.address);

}

main()
    .then(() => process.exit(0))
    .catch(error => {
        console.error(error);
        process.exit(1);
    }); 