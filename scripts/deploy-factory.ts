import hre from "hardhat";
import { Contract } from "ethers";
import * as dotenv from "dotenv";
dotenv.config();

async function main() {
  const [deployer] = await hre.ethers.getSigners();

  const OracleRegistryFactory = await hre.ethers.getContractFactory("OracleRegistry");
  const poolManagerAddress = deployer.address;
  console.log(`Deploying OracleRegistry with poolManager: ${poolManagerAddress}...`);

  const oracleRegistry = await OracleRegistryFactory.deploy(poolManagerAddress,);
  await oracleRegistry.deployed();
  console.log("OracleRegistry deployed to:", oracleRegistry.address);

  const PoolLogic = await hre.ethers.getContractFactory("UniswapV3Pool");
  const poolLogic = await PoolLogic.deploy();
  await poolLogic.deployed();
  console.log("UniswapV3Pool logic contract deployed to:", poolLogic.address);

  const Factory = await hre.ethers.getContractFactory("UniswapV3Factory");
  console.log("Deploying UniswapV3Factory proxy, ProxyAdmin, and implementation...");

  const initialOwnerForFactory = deployer.address;
  const oracleRegistryAddressForFactory = oracleRegistry.address;
  const poolLogicContractAddress = poolLogic.address;

  const factoryProxy: Contract = await hre.upgrades.deployProxy(
    Factory,
    [initialOwnerForFactory, oracleRegistryAddressForFactory, poolLogicContractAddress],
    {
      initializer: "initialize",
      kind: "transparent",
    }
  );
  await factoryProxy.deployed();

  console.log("UniswapV3Factory Proxy deployed to:", factoryProxy.address);

  const implementationAddress = await hre.upgrades.erc1967.getImplementationAddress(factoryProxy.address);
  console.log("UniswapV3Factory Implementation deployed to:", implementationAddress);

  const proxyAdminAddress = await hre.upgrades.erc1967.getAdminAddress(factoryProxy.address);
  console.log("ProxyAdmin for Factory deployed to:", proxyAdminAddress);

//   console.log(`\nTo upgrade, set FACTORY_PROXY_ADDRESS=${factoryProxy.address} in scripts/upgrade-factory.ts`);
//   console.log(`To verify on Etherscan (Sepolia example):
//   npx hardhat verify --network sepolia --constructor-args scripts/oracle-registry-args.js ${oracleRegistry.address} 
//   npx hardhat verify --network sepolia ${poolLogic.address}
//   npx hardhat verify --network sepolia ${implementationAddress} 
//   npx hardhat verify --network sepolia ${proxyAdminAddress}
//   npx hardhat verify --network sepolia ${factoryProxy.address}`);

}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 