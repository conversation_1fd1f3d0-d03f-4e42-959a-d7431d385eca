const { ethers } = require("hardhat");
import fs from "fs";
import Vault<PERSON>bi from './Vault.json';

async function main() {
  const config = JSON.parse(fs.readFileSync("deployed.json", "utf8"));

  const [deployer] = await ethers.getSigners();
  const peripheryAddress = config.NonfungiblePositionManager;
  const factoryAddress = config.Factory;
  const usdcAddress = config.USDC;
  const projectTokenAddress = config.LCoin;
  const swapRouterAddress = config.SwapRouter;
  const fee = 10000;
  const periphery = await ethers.getContractAt("NonfungiblePositionManager", peripheryAddress);
  const factory = await ethers.getContractAt("UniswapV3Factory", factoryAddress);


  const vaultAddress = "******************************************";

  const vaultContract = new ethers.Contract(
    vaultAddress,
    VaultAbi.abi,
    deployer
  );

  const POOL_MANAGER = process.env.POOL_MANAGER_PRIVATE_KEY;
  if (!POOL_MANAGER) {
    throw new Error("POOL_MANAGER_PRIVATE_KEY is not set");
  }
  const poolManager = new ethers.Wallet(POOL_MANAGER, ethers.provider);
  console.log("Using pool manager address:", poolManager.address);

  // Check if pool already exists
  let poolAddress = await factory.getPool(usdcAddress, projectTokenAddress, fee);
  if (poolAddress && poolAddress !== ethers.constants.AddressZero) {
    console.log("Pool already exists at:", poolAddress);
    return;
  }

  // write deploy mock vault code
  // const vaultMock = await ethers.getContractFactory("MockVault");
  // const vaultInstance = await vaultMock.deploy();
  // await vaultInstance.deployed();
  // console.log("MockVault deployed to:", vaultInstance.address);

  // // await vaultInstance.setAmmAddress(factoryAddress);
  // // console.log("Vault AMM set to:", factoryAddress);

  // await factory.setVault(vaultInstance.address);
  // console.log("Vault set to:", vaultInstance.address);  

  let vault = await factory.vault();
  console.log("Vault:", vault);

  // let vaultActive = await vaultContract.ammAddress();
  // console.log("Vault AMM:", vaultActive);

  // Check if oracle is set for both tokens
  try {
    const oracleRegistryAddr = await factory.oracleRegistry();
    const oracleRegistry = await ethers.getContractAt("OracleRegistry", oracleRegistryAddr);
    
    // Check USDC oracle
    const usdcOracle = await oracleRegistry.getOracle(usdcAddress);
    if (usdcOracle === ethers.constants.AddressZero) {
      console.error("Oracle not set for USDC. Please set the oracle in the factory.");
      return;
    } else {
      console.log("USDC Oracle:", usdcOracle);
    }
  } catch (e) {
    console.error("Error checking oracles:", e);
    return;
  }

  // Prepare launch params
  const now = Math.floor(Date.now() / 1000);
  const launchParams = {
    tokenLaunchType: 0, // FairLaunch
    exclusiveTradingPeriodStart: now + 600,
    exclusiveTradingPeriodEnd: now + 3600,
    extendedLiquidityLockDuration: 3600,
    projectManager: deployer.address,
    whitelistedAddressForSwap: swapRouterAddress
  };
  console.log("Launch params:", launchParams);

  console.log("Deployer address:", deployer.address);

  // Set periphery in factory if not already set
  try {
    const currentPeriphery = await factory.periphery();
    if (currentPeriphery !== peripheryAddress) {
      console.log("Setting periphery in factory...");
      await factory.setPeriphery(peripheryAddress);
      console.log("Periphery set successfully");
    } else {
      console.log("Periphery already set correctly");
    }
  } catch (e) {
    console.error("Error setting periphery:", e);
    return;
  }

  // Sort tokens by address
  const [token0, token1] = [usdcAddress, projectTokenAddress];

  // Prepare create pool params
  const sqrtPriceX96 = ethers.BigNumber.from("79228162514264337593543950336"); // 1:1 price
  const params = {
    token0,
    token1,
    fee,
    sqrtPriceX96,
    launchParams
  };

  // Try to create pool
  try {
    console.log("Calling createAndInitializePoolIfNecessaryWithParams on periphery...");
    console.log("Params:", params);
    const tx = await periphery.connect(poolManager).createAndInitializePoolIfNecessaryWithParams(params);
    const receipt = await tx.wait();
    // Try to get pool address from event or factory
    poolAddress = await factory.getPool(token0, token1, fee);
    console.log("Pool created at:", poolAddress);
    console.log("Tx hash:", tx.hash);
  } catch (err) {
    if (err && err.error && err.error.body) {
      try {
        const body = JSON.parse(err.error.body);
        if (body && body.error && body.error.message) {
          console.error("Revert reason:", body.error.message);
        }
      } catch (e) {}
    }
    console.error("Error creating pool via periphery:", err);
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
}); 

// Deploying core contracts with account: 0x5A22fA9238b53722486C33Ed5ab92a1AB9e86718
// USDC deployed to: 0xD2860F58B470Fe617021d8C86dE570b2642f0779
// Project Token deployed to: 0x45e151768f58F102DD9Ba4a1a56D6EE9EFF52A8d
// UniswapV3Pool logic deployed to: 0x4eaDb51DC1D2b91D2C0C337DC5d9eab5F9365B96
// UniswapV3Factory deployed to: 0xC4807E33650F63A2551dE2cFB5eE06C5E5C04cD6
// OracleRegistry deployed to: 0x0E1F0c3d7Fe1542D33fA96CB23D93278C9aA8c94
// Oracle registry updated in factory
// Oracle USDC set
// Using pool manager address: 0x5A22fA9238b53722486C33Ed5ab92a1AB9e86718
// NonfungiblePositionManager deployed to: 0x2A1784Bd37bfb7f33Fa4d36FE81045b8D49F96FF
// Periphery set in factory
// SwapRouter deployed to: 0x9Fbf3Fc7a1d2F4065A804CAba50Fa8F510f0340D

// Core Deployment Summary:
// USDC: 0xD2860F58B470Fe617021d8C86dE570b2642f0779
// Project Token: 0x45e151768f58F102DD9Ba4a1a56D6EE9EFF52A8d
// OracleRegistry: 0x0E1F0c3d7Fe1542D33fA96CB23D93278C9aA8c94
// UniswapV3Pool logic: 0x4eaDb51DC1D2b91D2C0C337DC5d9eab5F9365B96
// UniswapV3Factory: 0xC4807E33650F63A2551dE2cFB5eE06C5E5C04cD6
// SwapRouter: 0x9Fbf3Fc7a1d2F4065A804CAba50Fa8F510f0340D
// ✨  Done in 65.77s.
// shailendrasingh@Shailendras-MacBook-Air uni-v3-core % 