const hre = require("hardhat");
import fs from "fs";

async function main() {
  const [signer] = await hre.ethers.getSigners();
  console.log("Using address:", signer.address);

  const projectManagerPrivateKey = process.env.PROJECT_MANAGER_PRIVATE_KEY;
  if (!projectManagerPrivateKey) {
    throw new Error("PROJECT_MANAGER_PRIVATE_KEY is not set");
  }
  const projectManager = new hre.ethers.Wallet(projectManagerPrivateKey, hre.ethers.provider);
  console.log("Using project manager address:", projectManager.address);

  const config = JSON.parse(fs.readFileSync("deployed.json", "utf8"));

  const usdcAddress = config.USDC;
  const projectTokenAddress = config.LCoin;
  const peripheryAddress = config.NonfungiblePositionManager;
  const factoryAddress = config.Factory;

  const periphery = await hre.ethers.getContractAt("NonfungiblePositionManager", peripheryAddress);
  if (!usdcAddress || !projectTokenAddress || !peripheryAddress || !factoryAddress) {
    throw new Error("Missing environment variables");
  }
  // Sort tokens by address to match pool creation logic
  const [token0, token1] = [usdcAddress, projectTokenAddress];

  // Fetch factory and get pool address dynamically
  const factory = await hre.ethers.getContractAt("UniswapV3Factory", factoryAddress);
  const poolAddress = await factory.getPool(token0, token1, 10000);
  if (!poolAddress || poolAddress === hre.ethers.constants.AddressZero) {
    throw new Error("Pool does not exist. Please run the pool creation script first.");
  }
  const pool = await hre.ethers.getContractAt("UniswapV3Pool", poolAddress);

  await (await pool.setPeriphery(peripheryAddress)).wait();

  // Check pool initialization and get pool state
  const poolState = await pool.slot0();
  if (poolState.sqrtPriceX96.eq(0)) {
    throw new Error("Pool is not initialized. Please ensure pool creation script ran successfully.");
  }
  console.log('Current pool state:', {
    sqrtPriceX96: poolState.sqrtPriceX96.toString(),
    tick: poolState.tick,
    observationIndex: poolState.observationIndex,
    observationCardinality: poolState.observationCardinality,
    observationCardinalityNext: poolState.observationCardinalityNext,
    feeProtocol: poolState.feeProtocol,
    unlocked: poolState.unlocked
  });

  // Check exclusive trading period and whitelisting
  const exclusiveTradingPeriodStart = await pool.exclusiveTradingPeriodStart();
  const exclusiveTradingPeriodEnd = await pool.exclusiveTradingPeriodEnd();
  const whitelistedAddress = await pool.whitelistedAddressForSwap();
  // console.log(now)
  const now = Math.floor(Date.now() / 1000);
  console.log(now)
  console.log(exclusiveTradingPeriodStart)
  console.log(exclusiveTradingPeriodEnd)
  if (now >= exclusiveTradingPeriodStart && now <= exclusiveTradingPeriodEnd) {
      console.warn("Warning: You are not during the exclusive trading period. Liquidity addition may revert.");
      console.log(whitelistedAddress)
  }

  // Increase liquidity amounts
  const amount0Desired = hre.ethers.utils.parseUnits('300000', 6); // 10,000 USDC
  const amount1Desired = hre.ethers.utils.parseUnits('300000', 18); // 10,000 Project Token

  // Use wider tick range based on current tick
  const currentTick = poolState.tick;
  const tickSpacing = await pool.tickSpacing();
  const tickLower = -tickSpacing;
  const tickUpper = tickSpacing;

  console.log('Using tick range:', {
    currentTick,
    tickSpacing,
    tickLower,
    tickUpper
  });

  // // Compute expected liquidity using contract formula
  // const sqrtPriceX96 = poolState.sqrtPriceX96;
  // const sqrtRatioAX96 = pool.getSqrtRatioAtTick(tickLower);
  // const sqrtRatioBX96 = pool.getSqrtRatioAtTick(tickUpper);
  // const LiquidityAmounts = await hre.ethers.getContractFactory('LiquidityAmounts');
  // const liquidity = await LiquidityAmounts.attach(factoryAddress).getLiquidityForAmounts(
  //   sqrtPriceX96,
  //   sqrtRatioAX96,
  //   sqrtRatioBX96,
  //   amount0Desired,
  //   amount1Desired
  // );
  // console.log('Computed liquidity:', liquidity.toString());
  // if (liquidity.eq(0)) {
  //   console.error('Computed liquidity is zero. Check your tick range and amounts. Aborting mint.');
  //   return;
  // }


  // Check token balances
  const usdc = await hre.ethers.getContractAt("contracts/test/TestERC20.sol:TestERC20", usdcAddress);
  const projectToken = await hre.ethers.getContractAt("contracts/test/TestERC20.sol:TestERC20", projectTokenAddress);


  await (await usdc.mint(projectManager.address, hre.ethers.utils.parseUnits('250000', 6))).wait();
  await (await projectToken.mint(projectManager.address, hre.ethers.utils.parseUnits('250000', 18))).wait();

  const usdcBalance = await usdc.balanceOf(projectManager.address);
  const projectTokenBalance = await projectToken.balanceOf(projectManager.address);

  console.log("USDC Balance:", hre.ethers.utils.formatUnits(usdcBalance, 6));
  console.log("Project Token Balance:", hre.ethers.utils.formatUnits(projectTokenBalance, 18));

  if (usdcBalance.lt(amount0Desired) || projectTokenBalance.lt(amount1Desired)) {
    throw new Error("Insufficient token balances");
  }

  // Check existing allowances
  const usdcAllowance = await usdc.allowance(projectManager.address, peripheryAddress);
  const projectTokenAllowance = await projectToken.allowance(projectManager.address, peripheryAddress);
  
  console.log("Current USDC Allowance:", hre.ethers.utils.formatUnits(usdcAllowance, 6));
  console.log("Current Project Token Allowance:", hre.ethers.utils.formatUnits(projectTokenAllowance, 18));

  // Approve tokens
  console.log("Approving tokens...");
  const usdcApproveTx = await usdc.connect(projectManager).approve(peripheryAddress, amount0Desired.mul(2));
  await usdcApproveTx.wait();
  console.log("USDC approved");

  const projectTokenApproveTx = await projectToken.connect(projectManager).approve(peripheryAddress, amount1Desired.mul(2));
  await projectTokenApproveTx.wait();
  console.log("Project Token approved");

  // Verify approvals
  const newUsdcAllowance = await usdc.allowance(projectManager.address, peripheryAddress);
  const newProjectTokenAllowance = await projectToken.allowance(projectManager.address, peripheryAddress);
  console.log("New USDC Allowance:", hre.ethers.utils.formatUnits(newUsdcAllowance, 6));
  console.log("New Project Token Allowance:", hre.ethers.utils.formatUnits(newProjectTokenAllowance, 18));

  // Get current liquidity
  const liquidityBefore = await pool.liquidity();
  console.log("Liquidity before:", liquidityBefore.toString());

  // Add liquidity
  console.log("Adding liquidity via periphery...");
  const params = {
    token0: token0,  // Use sorted token addresses
    token1: token1,  // Use sorted token addresses
    fee: 10000,
    tickLower,
    tickUpper,
    amount0Desired: token0 === usdcAddress ? amount0Desired : amount1Desired,  // Match amounts to sorted tokens
    amount1Desired: token0 === usdcAddress ? amount1Desired : amount0Desired,  // Match amounts to sorted tokens
    amount0Min: 0,
    amount1Min: 0,
    recipient: projectManager.address,
    deadline: Math.floor(Date.now() / 1000) + 60 * 20 // 20 minutes
  };

  console.log("Params:", {
    ...params,
    amount0Desired: hre.ethers.utils.formatUnits(params.amount0Desired, 6),
    amount1Desired: hre.ethers.utils.formatUnits(params.amount1Desired, 18)
  });

  try {
    const tx = await periphery.connect(projectManager).mint(params);
    const receipt = await tx.wait();
    console.log("Liquidity added successfully! Transaction hash:", receipt.transactionHash);
  } catch (error) {
    console.error("Error adding liquidity:", error);
  }

  // Check final liquidity
  const liquidityAfter = await pool.liquidity();
  console.log("Liquidity after:", liquidityAfter.toString());
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  }); 

  // Liquidity added successfully! Transaction hash: 0x0cdfc9e5a6daeb4888101aefb8ed98f584b3b112b19cee03d5823aeabe9e246d