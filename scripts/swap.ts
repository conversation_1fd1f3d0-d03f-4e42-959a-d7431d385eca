const hre = require("hardhat");
import fs from "fs";

async function main() {
  // Load deployed addresses
  const config = JSON.parse(fs.readFileSync("deployed.json", "utf8"));
  const fee = 10000;

  // Get contracts at deployed addresses
  const factory = await hre.ethers.getContractAt("UniswapV3Factory", config.Factory);
  const usdc = await hre.ethers.getContractAt("TestERC20", config.USDC);
  const lcoin = await hre.ethers.getContractAt("TestERC20", config.LCoin);
  const swapRouter = await hre.ethers.getContractAt("SwapRouter", config.SwapRouter);
  const pool = await hre.ethers.getContractAt("UniswapV3Pool", config.Pool);
  const [deployer] = await hre.ethers.getSigners();
  console.log("Deployer:", deployer.address);
  console.log("USDC:", usdc.address);
  console.log("LCoin:", lcoin.address);
  console.log("SwapRouter:", swapRouter.address);
  console.log("Pool:", pool.address);

  console.log("project manager address:", await pool.projectManager());

//   await (await factory.setOracle(usdc.address, "******************************************")).wait();
//   console.log("Oracle USDC set");

  // Print balances and allowances
  const usdcBalance = await usdc.balanceOf(deployer.address);
  const lcoinBalance = await lcoin.balanceOf(deployer.address);
  const usdcAllowance = await usdc.allowance(deployer.address, swapRouter.address);
  const lcoinAllowance = await lcoin.allowance(deployer.address, swapRouter.address);
  console.log("USDC balance:", hre.ethers.utils.formatUnits(usdcBalance, 6));
  console.log("LCoin balance:", hre.ethers.utils.formatUnits(lcoinBalance, 18));
  console.log("USDC allowance for swapRouter:", usdcAllowance.toString());
  console.log("LCoin allowance for swapRouter:", lcoinAllowance.toString());

  // Approve tokens for swap if needed
  if (usdcAllowance.lt(hre.ethers.utils.parseUnits("100", 6))) {
    const tx = await usdc.approve(swapRouter.address, hre.ethers.constants.MaxUint256);
    await tx.wait();
    console.log("USDC approved for swapRouter");
  }
  if (lcoinAllowance.lt(hre.ethers.utils.parseUnits("1", 18))) {
    const tx = await lcoin.approve(swapRouter.address, hre.ethers.constants.MaxUint256);
    await tx.wait();
    console.log("LCoin approved for swapRouter");
  }

  // Check ETP start
  const etpStart = await pool.exclusiveTradingPeriodStart();
  const currentBlock = await hre.ethers.provider.getBlock('latest');
  const currentTime = currentBlock.timestamp;
  if (currentTime < etpStart.toNumber()) {
    console.log(`ETP (exclusive trading period) has not started yet. Current block timestamp: ${currentTime}, ETP start: ${etpStart.toNumber()}`);
    console.log('Please wait until the ETP has started before running the swap.');
    return;
  }
  console.log("ETP has started");
  // Run swap
  const swapParams = {
    tokenIn: usdc.address,
    tokenOut: lcoin.address,
    fee,
    recipient: deployer.address,
    deadline: Math.floor(Date.now() / 1000) + 3600,
    amountIn: hre.ethers.utils.parseUnits("100", 6),
    amountOutMinimum: 0,
    sqrtPriceLimitX96: 0
  };
  try {
    await swapRouter.callStatic.exactInputSingle(swapParams)
    const tx = await swapRouter.exactInputSingle(swapParams);
    const receipt = await tx.wait();
    console.log("Swap successful! Tx hash:", receipt.transactionHash);
  } catch (e) {
    console.error("Swap failed:", e.message);
    // Print pool state for debugging
    const liquidity = await pool.liquidity();
    const slot0 = await pool.slot0();
    const isWhitelisted = await pool.whitelistedAddressForSwap();
    const etpActive = await pool.exclusiveTradingPeriodStart();
    console.error("Pool state:", { liquidity: liquidity.toString(), slot0, isWhitelisted, etpActive });
    process.exit(1);
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
}); 


// Swap successful! Tx hash: 0x0673530878b3f30e1a65e5f07ca3011b587b7efac79462aa31d74019008d6c1a