import { ethers } from 'ethers';

interface AddLiquidityParams {
  token0: string;
  token1: string;
  fee: number;
  tickLower: number;
  tickUpper: number;
  amount0Desired: string;
  amount1Desired: string;
  amount0Min: string;
  amount1Min: string;
  recipient: string;
  deadline: number;
}

interface TokenInfo {
  address: string;
  decimals: number;
}

export async function addLiquidity(
  provider: ethers.providers.Provider,
  signer: ethers.Signer,
  peripheryAddress: string,
  token0Info: TokenInfo,
  token1Info: TokenInfo,
  params: AddLiquidityParams
) {
  // Create contract instances
  const periphery = new ethers.Contract(
    peripheryAddress,
    [
      "function mint(tuple(address token0, address token1, uint24 fee, int24 tickLower, int24 tickUpper, uint256 amount0Desired, uint256 amount1Desired, uint256 amount0Min, uint256 amount1Min, address recipient, uint256 deadline)) external payable returns (uint256 tokenId, uint128 liquidity, uint256 amount0, uint256 amount1)"
    ],
    signer
  );

  // Create token contracts for approvals
  const token0 = new ethers.Contract(
    token0Info.address,
    [
      "function approve(address spender, uint256 amount) external returns (bool)",
      "function allowance(address owner, address spender) external view returns (uint256)"
    ],
    signer
  );

  const token1 = new ethers.Contract(
    token1Info.address,
    [
      "function approve(address spender, uint256 amount) external returns (bool)",
      "function allowance(address owner, address spender) external view returns (uint256)"
    ],
    signer
  );

  // Check and set approvals
  const amount0Desired = ethers.utils.parseUnits(params.amount0Desired, token0Info.decimals);
  const amount1Desired = ethers.utils.parseUnits(params.amount1Desired, token1Info.decimals);

  const allowance0 = await token0.allowance(await signer.getAddress(), peripheryAddress);
  const allowance1 = await token1.allowance(await signer.getAddress(), peripheryAddress);

  if (allowance0.lt(amount0Desired)) {
    const tx = await token0.approve(peripheryAddress, ethers.constants.MaxUint256);
    await tx.wait();
  }

  if (allowance1.lt(amount1Desired)) {
    const tx = await token1.approve(peripheryAddress, ethers.constants.MaxUint256);
    await tx.wait();
  }

  // Prepare mint parameters
  const mintParams = {
    token0: params.token0,
    token1: params.token1,
    fee: params.fee,
    tickLower: params.tickLower,
    tickUpper: params.tickUpper,
    amount0Desired,
    amount1Desired,
    amount0Min: ethers.utils.parseUnits(params.amount0Min, token0Info.decimals),
    amount1Min: ethers.utils.parseUnits(params.amount1Min, token1Info.decimals),
    recipient: params.recipient,
    deadline: params.deadline
  };

  try {
    // Add liquidity
    const tx = await periphery.mint(mintParams);
    const receipt = await tx.wait();

    // Parse the event to get the position details
    const event = receipt.events.find((e: any) => e.event === 'IncreaseLiquidity');
    
    return {
      tokenId: event.args.tokenId.toString(),
      liquidity: event.args.liquidity.toString(),
      amount0: ethers.utils.formatUnits(event.args.amount0, token0Info.decimals),
      amount1: ethers.utils.formatUnits(event.args.amount1, token1Info.decimals),
      txHash: tx.hash
    };
  } catch (error) {
    throw new Error(`Failed to add liquidity: ${error.message}`);
  }
}

// Example usage:
/*
const provider = new ethers.providers.Web3Provider(window.ethereum);
const signer = provider.getSigner();

const token0Info = {
  address: "******************************************", // USDC
  decimals: 6
};

const token1Info = {
  address: "******************************************", // Project Token
  decimals: 18
};

const params = {
  token0: token0Info.address,
  token1: token1Info.address,
  fee: 3000,
  tickLower: -60, // Example tick range
  tickUpper: 60,
  amount0Desired: "100000", // 100,000 USDC
  amount1Desired: "100000", // 100,000 Project Token
  amount0Min: "99000", // 1% slippage
  amount1Min: "99000", // 1% slippage
  recipient: await signer.getAddress(),
  deadline: Math.floor(Date.now() / 1000) + 60 * 20 // 20 minutes
};

try {
  const result = await addLiquidity(
    provider,
    signer,
    "******************************************", // periphery address
    token0Info,
    token1Info,
    params
  );
  console.log("Liquidity added successfully:", result);
} catch (error) {
  console.error("Error adding liquidity:", error);
}
*/
