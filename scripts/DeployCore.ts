const { ethers, upgrades } = require("hardhat");

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("Deploying core contracts with account:", deployer.address);
  const USDCOracle = "******************************************";
  // Deploy test tokens
  const USDC = await ethers.getContractFactory("contracts/test/TestERC20.sol:TestERC20");
  const usdc = await USDC.deploy(ethers.utils.parseUnits("1000000", 6), 6);
  await usdc.deployed();
  console.log("USDC deployed to:", usdc.address);

  const ProjectToken = await ethers.getContractFactory("contracts/test/TestERC20.sol:TestERC20");
  const projectToken = await ProjectToken.deploy(ethers.utils.parseUnits("1000000", 18), 18);
  await projectToken.deployed();
  console.log("Project Token deployed to:", projectToken.address);

  // Deploy UniswapV3Pool logic contract
  const UniswapV3Pool = await ethers.getContractFactory("UniswapV3Pool");
  const poolLogic = await UniswapV3Pool.deploy();
  await poolLogic.deployed();
  console.log("UniswapV3Pool logic deployed to:", poolLogic.address);

  // Deploy UniswapV3Factory (Upgradeable)
  const UniswapV3Factory = await ethers.getContractFactory("UniswapV3Factory");
  const factory = await upgrades.deployProxy(
    UniswapV3Factory,
    [
      deployer.address,  // initialOwner
      ethers.constants.AddressZero, // oracleRegistry (set later)
      poolLogic.address
    ],
    { initializer: "initialize", kind: "transparent" }
  );
  await factory.deployed();
  console.log("UniswapV3Factory deployed to:", factory.address);


  // Deploy OracleRegistry
  const OracleRegistry = await ethers.getContractFactory("OracleRegistry");
  const oracleRegistry = await OracleRegistry.deploy(factory.address);
  await oracleRegistry.deployed();
  console.log("OracleRegistry deployed to:", oracleRegistry.address);

  // Update factory with correct oracle registry
  await (await factory.setOracleRegistry(oracleRegistry.address)).wait();
  console.log("Oracle registry updated in factory");

  // Set up oracles
  await (await factory.setOracle(usdc.address, USDCOracle)).wait();
  console.log("Oracle USDC set");

  const WETH9 = process.env.WETH9_ADDRESS || "******************************************"; // mainnet WETH9 as placeholder
  const TOKEN_DESCRIPTOR = process.env.TOKEN_DESCRIPTOR_ADDRESS || "******************************************"; // placeholder
  const POOL_MANAGER = process.env.POOL_MANAGER_PRIVATE_KEY;
  if (!POOL_MANAGER) {
    throw new Error("POOL_MANAGER_PRIVATE_KEY is not set");
  }
  const poolManager = new ethers.Wallet(POOL_MANAGER, ethers.provider);
  console.log("Using pool manager address:", poolManager.address);

  const NonfungiblePositionManager = await ethers.getContractFactory("NonfungiblePositionManager");
  const periphery = await NonfungiblePositionManager.deploy(
    factory.address,
    WETH9,
    TOKEN_DESCRIPTOR,
    poolManager.address
  );
  await periphery.deployed();
  console.log("NonfungiblePositionManager deployed to:", periphery.address);

  await (await factory.setPeriphery(periphery.address)).wait();
  console.log("Periphery set in factory");

  // Deploy SwapRouter
  const SwapRouter = await ethers.getContractFactory("SwapRouter");
  const swapRouter = await SwapRouter.deploy(factory.address, WETH9);
  await swapRouter.deployed();
  console.log("SwapRouter deployed to:", swapRouter.address);

  // Output all addresses
  console.log("\nCore Deployment Summary:");
  console.log("USDC:", usdc.address);
  console.log("Project Token:", projectToken.address);
  console.log("OracleRegistry:", oracleRegistry.address);
  console.log("UniswapV3Pool logic:", poolLogic.address);
  console.log("UniswapV3Factory:", factory.address);
  console.log("SwapRouter:", swapRouter.address);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
}); 