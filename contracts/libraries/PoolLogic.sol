// SPDX-License-Identifier: BUSL-1.1
pragma solidity =0.7.6;

import '../libraries/Position.sol';
import '../libraries/Tick.sol';
import '../libraries/LiquidityMath.sol';
import '../libraries/SqrtPriceMath.sol';
import '../libraries/TickMath.sol';
import '../libraries/Oracle.sol';
import '../libraries/SafeCast.sol';
import '../libraries/TickBitmap.sol';

library PoolLogic {
    using Position for mapping(bytes32 => Position.Info);
    using Position for Position.Info;
    using Tick for mapping(int24 => Tick.Info);
    using Oracle for Oracle.Observation[65535];
    using SafeCast for int256;
    using SafeCast for uint256;
    using TickBitmap for mapping(int16 => uint256);

    struct ModifyPositionParams {
        address owner;
        int24 tickLower;
        int24 tickUpper;
        int128 liquidityDelta;
    }

    struct Slot0Params {
        uint160 sqrtPriceX96;
        int24 tick;
        uint16 observationIndex;
        uint16 observationCardinality;
        uint16 observationCardinalityNext;
    }

    struct FeeGrowthGlobals {
        uint256 feeGrowthGlobal0X128;
        uint256 feeGrowthGlobal1X128;
    }

    struct ModifyPositionContext {
        Slot0Params slot0Params;
        uint128 maxLiquidityPerTick;
        int24 tickSpacing;
    }

    /// @notice Updates a position with the given liquidity delta and returns the storage pointer to the position
    function updatePosition(
        mapping(bytes32 => Position.Info) storage positions,
        mapping(int24 => Tick.Info) storage ticks,
        mapping(int16 => uint256) storage tickBitmap,
        Oracle.Observation[65535] storage observations,
        FeeGrowthGlobals memory feeGrowthGlobals,
        uint128 liquidity,
        uint128 maxLiquidityPerTick,
        int24 tickSpacing,
        int24 currentTick,
        ModifyPositionParams memory params,
        function() internal view returns (uint32) _blockTimestamp
    ) internal returns (Position.Info storage position) {
        position = positions.get(params.owner, params.tickLower, params.tickUpper);
        bool flippedLower;
        bool flippedUpper;
        if (params.liquidityDelta != 0) {
            uint32 time = _blockTimestamp();
            (int56 tickCumulative, uint160 secondsPerLiquidityCumulativeX128) = observations.observeSingle(
                time,
                0,
                currentTick,
                0, // observationIndex, not needed for tick update
                liquidity,
                0 // observationCardinality, not needed for tick update
            );
            flippedLower = ticks.update(
                params.tickLower,
                currentTick,
                params.liquidityDelta,
                feeGrowthGlobals.feeGrowthGlobal0X128,
                feeGrowthGlobals.feeGrowthGlobal1X128,
                secondsPerLiquidityCumulativeX128,
                tickCumulative,
                time,
                false,
                maxLiquidityPerTick
            );
            flippedUpper = ticks.update(
                params.tickUpper,
                currentTick,
                params.liquidityDelta,
                feeGrowthGlobals.feeGrowthGlobal0X128,
                feeGrowthGlobals.feeGrowthGlobal1X128,
                secondsPerLiquidityCumulativeX128,
                tickCumulative,
                time,
                true,
                maxLiquidityPerTick
            );
            if (flippedLower) {
                TickBitmap.flipTick(tickBitmap, params.tickLower, tickSpacing);
            }
            if (flippedUpper) {
                TickBitmap.flipTick(tickBitmap, params.tickUpper, tickSpacing);
            }
        }
        (uint256 feeGrowthInside0X128, uint256 feeGrowthInside1X128) = ticks.getFeeGrowthInside(
            params.tickLower,
            params.tickUpper,
            currentTick,
            feeGrowthGlobals.feeGrowthGlobal0X128,
            feeGrowthGlobals.feeGrowthGlobal1X128
        );
        position.update(params.liquidityDelta, feeGrowthInside0X128, feeGrowthInside1X128);
        if (params.liquidityDelta < 0) {
            if (flippedLower) {
                ticks.clear(params.tickLower);
            }
            if (flippedUpper) {
                ticks.clear(params.tickUpper);
            }
        }
    }

    /// @notice Calculates the amounts and updates observations for a position modification
    function calculateAmountsAndUpdateObs(
        Oracle.Observation[65535] storage observations,
        uint128 liquidity,
        Slot0Params memory slot0Params,
        int24 tickLower,
        int24 tickUpper,
        int128 liquidityDelta,
        function() internal view returns (uint32) _blockTimestamp
    )
        internal
        returns (
            int256 amount0,
            int256 amount1,
            uint128 newLiquidity,
            uint16 newObservationIndex,
            uint16 newObservationCardinality
        )
    {
        amount0 = 0;
        amount1 = 0;
        newLiquidity = liquidity;
        newObservationIndex = slot0Params.observationIndex;
        newObservationCardinality = slot0Params.observationCardinality;

        if (liquidityDelta != 0) {
            if (slot0Params.tick < tickLower) {
                amount0 = SqrtPriceMath.getAmount0Delta(
                    TickMath.getSqrtRatioAtTick(tickLower),
                    TickMath.getSqrtRatioAtTick(tickUpper),
                    liquidityDelta
                );
            } else if (slot0Params.tick < tickUpper) {
                uint128 liquidityBefore = liquidity;
                (newObservationIndex, newObservationCardinality) = observations.write(
                    slot0Params.observationIndex,
                    _blockTimestamp(),
                    slot0Params.tick,
                    liquidityBefore,
                    slot0Params.observationCardinality,
                    slot0Params.observationCardinalityNext
                );
                amount0 = SqrtPriceMath.getAmount0Delta(
                    slot0Params.sqrtPriceX96,
                    TickMath.getSqrtRatioAtTick(tickUpper),
                    liquidityDelta
                );
                amount1 = SqrtPriceMath.getAmount1Delta(
                    TickMath.getSqrtRatioAtTick(tickLower),
                    slot0Params.sqrtPriceX96,
                    liquidityDelta
                );
                newLiquidity = LiquidityMath.addDelta(liquidityBefore, liquidityDelta);
            } else {
                amount1 = SqrtPriceMath.getAmount1Delta(
                    TickMath.getSqrtRatioAtTick(tickLower),
                    TickMath.getSqrtRatioAtTick(tickUpper),
                    liquidityDelta
                );
            }
        }
    }
} 