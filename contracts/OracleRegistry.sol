// SPDX-License-Identifier: BUSL-1.1
pragma solidity =0.7.6;
import "./interfaces/IOracleRegistry.sol";

interface AggregatorV3Interface {
    function latestRoundData()
        external
        view
        returns (
            uint80 roundId,
            int256 answer,
            uint256 startedAt,
            uint256 updatedAt,
            uint80 answeredInRound
        );
}

/// @title OracleRegistry
/// @notice Registry of token oracles   
contract OracleRegistry is IOracleRegistry {
    mapping(address => address) public tokenOracles;
    address public uniswapV3Factory;
    
    constructor(address _uniswapV3Factory) {
        uniswapV3Factory = _uniswapV3Factory;
    }

    modifier onlyFactory() {
        require(msg.sender == uniswapV3Factory, "Only Factory contract allowed");
        _;
    }

    function setFactory(address _factory) external {
        require(msg.sender == uniswapV3Factory, "Only current factory can update");
        uniswapV3Factory = _factory;
    }

    /// @inheritdoc IOracleRegistry
    function setOracle(address token, address oracle) external override onlyFactory {
        require(token != address(0), "ZTA");
        tokenOracles[token] = oracle;
    }

    /// @inheritdoc IOracleRegistry
    function getOracle(address token) external view override returns ( address oracle) {
        return _getOracle(token);
    }

    function _getOracle(address token) internal view returns ( address oracle) {
        require(token != address(0), "ZTA");
        if (tokenOracles[token] != address(0)) {
            return (tokenOracles[token]);
        }
        return (address(0));
    }

    function getPrice(address token) external view override returns (uint256) {
        if (token == address(0)) {
            return 0;
        }
        address oracle = _getOracle(token);
        AggregatorV3Interface oracleFeed = AggregatorV3Interface(oracle);
        (
            ,
            int256 price,
            ,
            ,
        ) = oracleFeed.latestRoundData();
        return uint256(price);
    }
}