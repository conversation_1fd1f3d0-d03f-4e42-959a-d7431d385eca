// SPDX-License-Identifier: BUSL-1.1
pragma solidity =0.7.6;
pragma abicoder v2;

import './interfaces/IUniswapV3Factory.sol';
import './interfaces/IOracleRegistry.sol';
import './interfaces/types/LaunchTypes.sol';

import '@openzeppelin/contracts-upgradeable/proxy/Initializable.sol';
import '@openzeppelin/contracts/proxy/UpgradeableBeacon.sol';
import '@openzeppelin/contracts/proxy/BeaconProxy.sol';
import {AccessControl} from '@openzeppelin/contracts/access/AccessControl.sol';

import './UniswapV3Pool.sol';

// Define IntentTypesLib struct
library IntentTypesLib {
    struct IntentProcessorBoundMessageLaunchpadPoolCreationData {
        bytes32 poolAddress;
        bytes32 token0;
        bytes32 token1;
        uint256 chainId;
        uint256 launchType;
        bytes32 projectLiquidityProvider;
        uint256 feeTier;
        uint8 reserveTokenIndex;
        uint256 exclusiveTradingPeriodStartTimestamp;
        uint256 exclusiveTradingPeriodEndTimestamp;
    }
}

// Interface for vault contract
interface IVault {
    function onLaunchpadPoolCreation(IntentTypesLib.IntentProcessorBoundMessageLaunchpadPoolCreationData memory data
    ) external returns (bool callSucceeded, bytes memory callResult);
}

/// @title Canonical Uniswap V3 factory
/// @notice Deploys Uniswap V3 pools and manages ownership and control over pool protocol fees
contract UniswapV3Factory is Initializable, IUniswapV3Factory, AccessControl {
    bytes32 public constant POOL_MANAGER_ROLE = keccak256('POOL_MANAGER_ROLE');
    bytes32 public constant OWNER_ROLE = keccak256('OWNER_ROLE');

    /// @notice The address of the oracle registry
    address public oracleRegistry;

    /// @notice The address of the current UniswapV3Pool logic contract implementation
    address public poolLogicContract;

    /// @notice The beacon for UniswapV3Pools
    UpgradeableBeacon public poolBeacon;

    /// @notice The periphery address
    address public override periphery;

    /// @inheritdoc IUniswapV3Factory
    mapping(uint24 => int24) public override feeAmountTickSpacing;
    /// @inheritdoc IUniswapV3Factory
    mapping(address => mapping(address => mapping(uint24 => address))) public override getPool;

    /// @notice The vault contract address
    address public override vault;

    event PoolLogicContractChanged(address oldLogic, address newLogic);
    event VaultChanged(address oldVault, address newVault);

    modifier onlyRole(bytes32 role) {
        require(hasRole(role, msg.sender), 'Unauthorized');
        _;
    }

    modifier onlyPeriphery() {
        require(msg.sender == periphery, "NP");
        _;
    }

    function initialize(
        address _initialOwner,
        address _oracleRegistry,
        address _poolLogicContract
    ) public initializer {
        _setupRole(DEFAULT_ADMIN_ROLE, _initialOwner);
        grantRole(OWNER_ROLE, _initialOwner);
        oracleRegistry = _oracleRegistry;
        poolLogicContract = _poolLogicContract;
        require(_poolLogicContract != address(0), 'PLC_INIT');
        poolBeacon = new UpgradeableBeacon(_poolLogicContract);
        enableFeeAmount(500, 10);
        enableFeeAmount(3000, 60);
        enableFeeAmount(10000, 200);
        enableFeeAmount(30000, 60);
    }

    /// @inheritdoc IUniswapV3Factory
    function createPool(
        address token0,
        address token1,
        uint24 fee,
        PoolLaunchParameters memory launchParams
    ) external override onlyPeriphery returns (address pool) {
        require(token0 != token1, 'SO');
        require(address(poolBeacon) != address(0), 'PBNZ');
        require(token0 != address(0) && token1 != address(0), 'ZLA');
        address oracle = IOracleRegistry(oracleRegistry).getOracle(token0);
        require(oracle != address(0), 'ONS');
        require(launchParams.projectManager != address(0), 'PMR');

        int24 tickSpacing = feeAmountTickSpacing[fee];
        require(tickSpacing != 0, 'FNT');
        require(getPool[token0][token1][fee] == address(0), 'PAE');
        require(getPool[token1][token0][fee] == address(0), 'PAE');

        // Data for initializing the new pool proxy
        bytes memory initializeData = abi.encodeWithSelector(
            IUniswapV3Pool.initializePool.selector,
            address(this),
            token0,
            token1,
            fee,
            tickSpacing,
            oracleRegistry,
            launchParams
        );

        // Create a BeaconProxy pointing to the poolBeacon
        pool = address(new BeaconProxy(address(poolBeacon), initializeData));

        getPool[token0][token1][fee] = pool;
        getPool[token1][token0][fee] = pool;
        emit PoolCreated(token0, token1, fee, tickSpacing, pool, launchParams);

        // Vault call is required
        require(vault != address(0), 'VNS'); // Vault Not Set
        _notifyVault(pool, token0, token1, fee, launchParams);
    }

    /// @notice Get the current chain ID
    function getChainId() internal pure returns (uint256) {
        uint256 chainId;
        assembly {
            chainId := chainid()
        }
        return chainId;
    }

    /// @notice Internal function to notify vault of pool creation
    function _notifyVault(
        address pool,
        address token0,
        address token1,
        uint24 fee,
        PoolLaunchParameters memory launchParams
    ) internal {
        IntentTypesLib.IntentProcessorBoundMessageLaunchpadPoolCreationData memory data = 
            IntentTypesLib.IntentProcessorBoundMessageLaunchpadPoolCreationData({
                poolAddress: bytes32(uint256(uint160(pool))),
                token0: bytes32(uint256(uint160(token0))),
                token1: bytes32(uint256(uint160(token1))),
                chainId: getChainId(),
                launchType: uint256(launchParams.tokenLaunchType),
                projectLiquidityProvider: bytes32(uint256(uint160(launchParams.projectManager))),
                feeTier: uint256(fee),
                reserveTokenIndex: 0, // Default to 0, could be parameterized if needed
                exclusiveTradingPeriodStartTimestamp: launchParams.exclusiveTradingPeriodStart,
                exclusiveTradingPeriodEndTimestamp: launchParams.exclusiveTradingPeriodEnd
            });

        (bool callSucceeded, ) = IVault(vault).onLaunchpadPoolCreation(data);
        require(callSucceeded, 'VCF'); // Vault Call Failed
    }

    /// @notice Sets the vault contract address
    /// @param _vault The address of the vault contract
    function setVault(address _vault) external override onlyRole(OWNER_ROLE) {
        address oldVault = vault;
        vault = _vault;
        emit VaultChanged(oldVault, _vault);
    }

    /// @notice Sets or updates the address of the UniswapV3Pool logic contract.
    /// @param _newPoolLogicContract The address of the new pool logic contract.
    function setPoolLogicContract(address _newPoolLogicContract) external onlyRole(OWNER_ROLE) {
        require(_newPoolLogicContract != address(0), 'ZLA');
        require(address(poolBeacon) != address(0), 'PBNZ_SLC'); 

        address oldLogic = poolLogicContract;
        poolBeacon.upgradeTo(_newPoolLogicContract);
        poolLogicContract = _newPoolLogicContract;

        emit PoolLogicContractChanged(oldLogic, _newPoolLogicContract);
    }

    /// @inheritdoc IUniswapV3Factory
    function setOracleRegistry(address _oracleRegistry) external override onlyRole(OWNER_ROLE) {
        oracleRegistry = _oracleRegistry;
    }

    /// @inheritdoc IUniswapV3Factory
    function setOracle(address token, address oracle) external override onlyRole(OWNER_ROLE) {
        IOracleRegistry(oracleRegistry).setOracle(token, oracle);
    }

    /// @inheritdoc IUniswapV3Factory
    function enableFeeAmount(uint24 fee, int24 tickSpacing) public override onlyRole(OWNER_ROLE) {
        require(fee < 1000000);
        // tick spacing is capped at 16384 to prevent the situation where tickSpacing is so large that
        // TickBitmap#nextInitializedTickWithinOneWord overflows int24 container from a valid tick
        // 16384 ticks represents a >5x price change with ticks of 1 bips
        require(tickSpacing > 0 && tickSpacing < 16384);
        require(feeAmountTickSpacing[fee] == 0);

        feeAmountTickSpacing[fee] = tickSpacing;
        emit FeeAmountEnabled(fee, tickSpacing);
    }

    function setPeriphery(address _periphery) external override onlyRole(OWNER_ROLE) {
        require(_periphery != address(0), "ZP");
        periphery = _periphery;
    }

    uint256[49] private __gap;
}