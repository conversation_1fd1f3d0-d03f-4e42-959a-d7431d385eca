// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity >=0.5.0;

/// @notice Enum for the type of token launch
enum TokenLaunchType {
    FairLaunch,
    CuratedLaunch
}

struct PoolLaunchParameters {
    TokenLaunchType tokenLaunchType;
    uint256 exclusiveTradingPeriodStart;
    uint256 exclusiveTradingPeriodEnd;
    uint256 extendedLiquidityLockDuration;
    address projectManager;
    address whitelistedAddressForSwap;
} 