// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity >=0.5.0;
pragma abicoder v2;

import './pool/IUniswapV3PoolImmutables.sol';
import './pool/IUniswapV3PoolState.sol';
import './pool/IUniswapV3PoolDerivedState.sol';
import './pool/IUniswapV3PoolActions.sol';
import './pool/IUniswapV3PoolOwnerActions.sol';
import './pool/IUniswapV3PoolEvents.sol';
import './types/LaunchTypes.sol';

/// @title The interface for a Uniswap V3 Pool
/// @notice A Uniswap pool facilitates swapping and automated market making between any two assets that strictly conform
/// to the ERC20 specification
/// @dev The pool interface is broken up into many smaller pieces
interface IUniswapV3Pool is
    IUniswapV3PoolImmutables,
    IUniswapV3PoolState,
    IUniswapV3PoolDerivedState,
    IUniswapV3PoolActions,
    IUniswapV3PoolOwnerActions,
    IUniswapV3PoolEvents
{
    /// @notice Initializes the pool for upgradeable (proxy) usage.
    /// @dev This is the initializer called by the factory when a new pool proxy is deployed.
    /// It is defined here because its parameters are specific to the factory's call context.
    /// Other functions and events are inherited from the sub-interfaces.
    /// @param _factory The address of the factory contract
    /// @param _token0 The first token of the pool by address sort order.
    /// @param _token1 The second token of the pool by address sort order.
    /// @param _fee The fee collected upon every swap in the pool, denominated in hundredths of a bip.
    /// @param _tickSpacing The spacing between usable ticks.
    /// @param _launchParams Additional parameters for launching the pool, like ETP, ELLD.
    function initializePool(
        address _factory,
        address _token0,
        address _token1,
        uint24 _fee,
        int24 _tickSpacing,
        address _oracleRegistry,
        PoolLaunchParameters memory _launchParams
    ) external;

// In IUniswapV3Pool.sol
function requiredStableCoinLiquidityFairLaunch() external view returns (uint256);
function requiredStableCoinLiquidityCuratedLaunch() external view returns (uint256);
function updateRequiredStableCoinLiquidityFairLaunch(uint256 _requiredStableCoinLiquidityFairLaunch) external;
function updateRequiredStableCoinLiquidityCuratedLaunch(uint256 _requiredStableCoinLiquidityCuratedLaunch) external;
}
