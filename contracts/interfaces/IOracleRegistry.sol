// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity >=0.5.0;

/// @title OracleRegistry
/// @notice Registry of token oracles
interface IOracleRegistry {

    /// @notice Sets the oracle for the given token
    /// @param token The token to set the oracle for
    /// @param oracle The oracle to set for the given token
    function setOracle(address token, address oracle) external;

    /// @notice Gets the oracle for the given token
    /// @param token0 The token that the oracle is for
    /// @return oracle The oracle for the given token
    function getOracle(address token0) external view returns (address oracle);

    /// @notice Gets the price of the given token
    /// @param token0 The token that the price is for
    /// @return price The price of the given token
    function getPrice(address token0) external view returns (uint256 price);
}