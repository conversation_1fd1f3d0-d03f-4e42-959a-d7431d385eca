// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity >=0.5.0;

import '../types/LaunchTypes.sol';

/// @title Pool state that never changes
/// @notice These parameters are fixed for a pool forever, i.e., the methods will always return the same values
interface IUniswapV3PoolImmutables {
    /// @notice The contract that deployed the pool, which must adhere to the IUniswapV3Factory interface
    /// @return The contract address
    function factory() external view returns (address);

    /// @notice The first of the two tokens of the pool, sorted by address
    /// @return The token contract address
    function token0() external view returns (address);

    /// @notice The second of the two tokens of the pool, sorted by address
    /// @return The token contract address
    function token1() external view returns (address);

    /// @notice The pool's fee in hundredths of a bip, i.e. 1e-6
    /// @return The fee
    function fee() external view returns (uint24);

    /// @notice The PVM sets the exclusive trading period start timestamp for the pool
    /// @return The ETP start timestamp
    function exclusiveTradingPeriodStart() external view returns (uint256);

    /// @notice The PVM sets the exclusive trading period end timestamp for the pool
    /// @return The ETP end timestamp
    function exclusiveTradingPeriodEnd() external view returns (uint256);

    /// @notice The PVM sets the extended liquidity lock duration for the pool
    /// @return The extended liquidity lock duration
    function extendedLiquidityLockDuration() external view returns (uint256);

    /// @notice The pool token launch type
    /// @return The token launch type
    function tokenLaunchType() external view returns (TokenLaunchType);

    /// @notice The pool tick spacing
    /// @dev Ticks can only be used at multiples of this value, minimum of 1 and always positive
    /// e.g.: a tickSpacing of 3 means ticks can be initialized every 3rd tick, i.e., ..., -6, -3, 0, 3, 6, ...
    /// This value is an int24 to avoid casting even though it is always positive.
    /// @return The tick spacing
    function tickSpacing() external view returns (int24);

    /// @notice The maximum amount of liquidityGross that can be added to a tick
    /// @return The maximum amount of liquidityGross that can be added to the tick
    function maxLiquidityPerTick() external view returns (uint128);

    /// @notice The periphery for the pool
    /// @return The periphery
    function periphery() external view returns (address);

    /// @notice Set the periphery for the pool
    /// @param _periphery The address of the periphery
    function setPeriphery(address _periphery) external;

    /// @notice The project manager for the pool
    /// @return The project manager
    function projectManager() external view returns (address);
}
