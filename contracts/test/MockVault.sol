// SPDX-License-Identifier: BUSL-1.1
pragma solidity =0.7.6;
pragma abicoder v2;

// Define IntentTypesLib struct (same as in factory)
library IntentTypesLib {
    struct IntentProcessorBoundMessageLaunchpadPoolCreationData {
        bytes32 poolAddress;
        bytes32 token0;
        bytes32 token1;
        uint256 chainId;
        uint256 launchType;
        bytes32 projectLiquidityProvider;
        uint256 feeTier;
        uint8 reserveTokenIndex;
        uint256 exclusiveTradingPeriodStartTimestamp;
        uint256 exclusiveTradingPeriodEndTimestamp;
    }
}

contract MockVault {
    bool public shouldSucceed = true;
    bytes public lastCallResult = "";
    
    // Storage for the last call data received
    IntentTypesLib.IntentProcessorBoundMessageLaunchpadPoolCreationData public lastCallData;
    uint256 public callCount = 0;
    
    event VaultCalled(
        bytes32 poolAddress,
        bytes32 token0,
        bytes32 token1,
        uint256 chainId,
        uint256 launchType,
        bytes32 projectLiquidityProvider,
        uint256 feeTier,
        uint8 reserveTokenIndex,
        uint256 exclusiveTradingPeriodStartTimestamp,
        uint256 exclusiveTradingPeriodEndTimestamp
    );
    
    function onLaunchpadPoolCreation(
        IntentTypesLib.IntentProcessorBoundMessageLaunchpadPoolCreationData memory data
    ) external returns (bool callSucceeded, bytes memory callResult) {
        callCount++;
        lastCallData = data;
        
        emit VaultCalled(
            data.poolAddress,
            data.token0,
            data.token1,
            data.chainId,
            data.launchType,
            data.projectLiquidityProvider,
            data.feeTier,
            data.reserveTokenIndex,
            data.exclusiveTradingPeriodStartTimestamp,
            data.exclusiveTradingPeriodEndTimestamp
        );
        
        return (shouldSucceed, lastCallResult);
    }
    
    function setShouldSucceed(bool _shouldSucceed) external {
        shouldSucceed = _shouldSucceed;
    }
    
    function setCallResult(bytes memory _result) external {
        lastCallResult = _result;
    }
    
    function reset() external {
        callCount = 0;
        shouldSucceed = true;
        lastCallResult = "";
    }
} 