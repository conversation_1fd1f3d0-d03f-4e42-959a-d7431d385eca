// contracts/test/TestUniswapV2Factory.sol
// SPDX-License-Identifier: BUSL-1.1
pragma solidity =0.7.6;
pragma abicoder v2;

import "../UniswapV3Factory.sol"; // Assumes UniswapV3Factory.sol is in the same directory

// UniswapV3Factory already inherits Initializable.
contract TestUniswapV3FactoryV2 is UniswapV3Factory { // Renamed contract
    uint256 public factoryVersionNumber;
    bool private _initializedV2; // Boolean guard

    event FactoryUpgradedToV2(uint256 versionSet);

    // A reinitializer for V2 specific state.
    // Guarded by custom logic due to potential reinitializer() issues.
    function initializeV2(uint256 _version) public onlyRole(OWNER_ROLE) {
        require(!_initializedV2, "TestUniswapV3FactoryV2: already initialized V2");
        _initializedV2 = true;
        factoryVersionNumber = _version;
        emit FactoryUpgradedToV2(_version);
    }

    function getFactoryVersionNumber() external view returns (uint256) {
        return factoryVersionNumber;
    }

    // A simple marker function to identify V2
    function isV2() external pure returns (bool) {
        return true;
    }
}