// SPDX-License-Identifier: UNLICENSED
pragma solidity =0.7.6;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract TestERC20 is ERC20 {
    uint8 private _customDecimals;

    constructor(uint256 amountToMint, uint8 decimals_) ERC20("Test Token", "TST") {
        _customDecimals = decimals_;
        _mint(msg.sender, amountToMint);
    }

    function decimals() public view override returns (uint8) {
        return _customDecimals;
    }

    function mint(address to, uint256 amount) public {
        _mint(to, amount);
    }
}
